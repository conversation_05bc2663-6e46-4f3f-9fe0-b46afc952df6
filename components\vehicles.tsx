"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { getVehicleStatusConfig } from "@/lib/vehicle-status-config"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  Al<PERSON><PERSON>ialog<PERSON><PERSON><PERSON>,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Car, Search, Filter, Edit, Trash2, Settings2, Eye, Plus } from "lucide-react"
import { useVehicles } from "@/hooks/use-fleet-data"
import { LoadingSpinner } from "@/components/loading-spinner"
import { ErrorDisplay } from "@/components/error-display"
import { AddVehicleDialog } from "@/components/forms/add-vehicle-dialog"
import { EditVehicleDialog } from "@/components/forms/edit-vehicle-dialog"
import { VehicleProfileDialog } from "@/components/forms/vehicle-profile-dialog"

import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"
import { createClient } from "@/lib/supabase-browser"

interface VehiclesProps {
  language: "ar" | "en"
}

export function Vehicles({ language }: VehiclesProps) {
  const { data: vehicles, loading, error, refetch } = useVehicles(100)
  const [editingVehicle, setEditingVehicle] = useState<any>(null)
  const [deletingVehicle, setDeletingVehicle] = useState<any>(null)
  const [profileVehicle, setProfileVehicle] = useState<any>(null)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [manageColumnsOpen, setManageColumnsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [showFilters, setShowFilters] = useState(false)
  const [advancedFilters, setAdvancedFilters] = useState({
    vehicleType: "__all__",
    serviceType: "__all__",
    licenseType: "__all__",
    vehicleFeatures: "__all__",
    permits: "__all__",
    branchId: "__all__"
  })
  const [branches, setBranches] = useState<Array<{ id: string; name: string }>>([])
  const [uniqueVehicleTypes, setUniqueVehicleTypes] = useState<string[]>([])
  const [uniqueServiceTypes, setUniqueServiceTypes] = useState<string[]>([])
  const [uniqueLicenseTypes, setUniqueLicenseTypes] = useState<string[]>([])
  const [uniqueVehicleFeatures, setUniqueVehicleFeatures] = useState<string[]>([])
  const [uniquePermits, setUniquePermits] = useState<string[]>([])
  const { toast } = useToast()

  // Fetch unique values for dropdowns
  useEffect(() => {
    const fetchDropdownData = async () => {
      const supabaseClient = createClient()
      
      // Fetch branches
      try {
        const { data: branchesData, error: branchesError } = await supabaseClient
          .from('branches')
          .select('id, name')
          .order('name')
        
        if (!branchesError && branchesData) {
          setBranches(branchesData)
        }
      } catch (error) {
        console.error('Error fetching branches:', error)
      }
      
      // Fetch unique vehicle types
      try {
        const { data: vehicleTypesData, error: vehicleTypesError } = await supabaseClient
          .from('vehicles')
          .select('vehicle_type')
          .not('vehicle_type', 'is', null)
        
        if (!vehicleTypesError && vehicleTypesData) {
          const uniqueTypes = [...new Set(vehicleTypesData.map(item => item.vehicle_type).filter(Boolean))] as string[]
          setUniqueVehicleTypes(uniqueTypes)
        }
      } catch (error) {
        console.error('Error fetching vehicle types:', error)
      }
      
      // Fetch unique service types
      try {
        const { data: serviceTypesData, error: serviceTypesError } = await supabaseClient
          .from('vehicles')
          .select('service_type')
          .not('service_type', 'is', null)
        
        if (!serviceTypesError && serviceTypesData) {
          const uniqueTypes = [...new Set(serviceTypesData.map(item => item.service_type).filter(Boolean))] as string[]
          setUniqueServiceTypes(uniqueTypes)
        }
      } catch (error) {
        console.error('Error fetching service types:', error)
      }
      
      // Fetch unique license types
      try {
        const { data: licenseTypesData, error: licenseTypesError } = await supabaseClient
          .from('vehicles')
          .select('license_type')
          .not('license_type', 'is', null)
        
        if (!licenseTypesError && licenseTypesData) {
          const uniqueTypes = [...new Set(licenseTypesData.map(item => item.license_type).filter(Boolean))] as string[]
          setUniqueLicenseTypes(uniqueTypes)
        }
      } catch (error) {
        console.error('Error fetching license types:', error)
      }
      
      // Fetch unique vehicle features
      try {
        const { data: featuresData, error: featuresError } = await supabaseClient
          .from('vehicles')
          .select('vehicle_features')
          .not('vehicle_features', 'is', null)
        
        if (!featuresError && featuresData) {
          const uniqueFeatures = [...new Set(featuresData.map(item => item.vehicle_features).filter(Boolean))] as string[]
          setUniqueVehicleFeatures(uniqueFeatures)
        }
      } catch (error) {
        console.error('Error fetching vehicle features:', error)
      }
      
      // Fetch unique permits
      try {
        const { data: permitsData, error: permitsError } = await supabaseClient
          .from('vehicles')
          .select('permits')
          .not('permits', 'is', null)
        
        if (!permitsError && permitsData) {
          const uniquePermits = [...new Set(permitsData.map(item => item.permits).filter(Boolean))] as string[]
          setUniquePermits(uniquePermits)
        }
      } catch (error) {
        console.error('Error fetching permits:', error)
      }
    }
    
    fetchDropdownData()
  }, [])

  // Define all available columns with their keys and labels
  const allColumns = [
    { key: 'plate_number', label: language === "ar" ? "رقم اللوحة" : "Plate Number", defaultVisible: true },
    { key: 'vehicle_type', label: language === "ar" ? "نوع المركبة" : "Vehicle Type", defaultVisible: true },
    { key: 'service_type', label: language === "ar" ? "نوع الخدمة" : "Service Type", defaultVisible: true },
    { key: 'fuel_type', label: language === "ar" ? "نوع الوقود" : "Fuel Type", defaultVisible: true },
    { key: 'year', label: language === "ar" ? "السنة" : "Year", defaultVisible: true },
    { key: 'current_km', label: language === "ar" ? "الكيلومترات الحالية" : "Current KM", defaultVisible: true },
    { key: 'status', label: language === "ar" ? "الحالة" : "Status", defaultVisible: true },
    { key: 'next_maintenance_km', label: language === "ar" ? "كيلومترات الصيانة القادمة" : "Next Maintenance Km", defaultVisible: true },
    { key: 'color', label: language === "ar" ? "اللون" : "Color", defaultVisible: false },
    { key: 'vin', label: language === "ar" ? "رقم الهيكل" : "VIN", defaultVisible: false },
    { key: 'last_maintenance_km', label: language === "ar" ? "آخر صيانة (كم)" : "Last Maintenance KM", defaultVisible: false },
    { key: 'last_tire_change_km', label: language === "ar" ? "آخر تغيير إطارات (كم)" : "Last Tire Change KM", defaultVisible: false },
    { key: 'license_expiry', label: language === "ar" ? "انتهاء الرخصة" : "License Expiry", defaultVisible: false },
    { key: 'license_type', label: language === "ar" ? "نوع الترخيص" : "License Type", defaultVisible: false },
    { key: 'vehicle_features', label: language === "ar" ? "تفاصيل المركبة" : "Vehicle Features", defaultVisible: false },
    { key: 'permits', label: language === "ar" ? "التصاريح" : "Permits", defaultVisible: false },
    { key: 'branch_id', label: language === "ar" ? "الفرع" : "Branch", defaultVisible: false },
    { key: 'created_at', label: language === "ar" ? "تاريخ الإنشاء" : "Created At", defaultVisible: false },
  ]

  // Initialize visible columns with default values
  const [visibleColumns, setVisibleColumns] = useState<Record<string, boolean>>(() => {
    const defaultVisible: Record<string, boolean> = {}
    allColumns.forEach(col => {
      defaultVisible[col.key] = col.defaultVisible
    })
    return defaultVisible
  })

  if (loading) {
    return <LoadingSpinner className="h-64" />
  }

  if (error) {
    return <ErrorDisplay error={error} />
  }

  const getStatusBadge = (status: string) => {
    const config = getVehicleStatusConfig(status, language);
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const handleEditVehicle = (vehicle: any) => {
    setEditingVehicle(vehicle)
  }

  const handleViewProfile = (vehicle: any) => {
    setProfileVehicle(vehicle)
  }

  const handleDeleteVehicle = (vehicle: any) => {
    setDeletingVehicle(vehicle)
  }

  const confirmDeleteVehicle = async () => {
    if (!deletingVehicle) return

    setDeleteLoading(true)
    const supabaseClient = createClient()
    try {
      const { error } = await supabaseClient
        .from("vehicles")
        .delete()
        .eq("id", deletingVehicle.id)

      if (error) {
        throw error
      }

      toast({
        title: language === "ar" ? "تم حذف المركبة" : "Vehicle Deleted",
        description:
          language === "ar"
            ? `تم حذف المركبة ${deletingVehicle.plate_number} بنجاح`
            : `Vehicle ${deletingVehicle.plate_number} has been deleted successfully`,
      })

      // Refresh the vehicles list
      refetch()
      setDeletingVehicle(null)
    } catch (err: any) {
      console.error("Error deleting vehicle:", err)
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description: err.message || (language === "ar" ? "حدث خطأ أثناء حذف المركبة" : "An error occurred while deleting the vehicle"),
        variant: "destructive",
      })
    } finally {
      setDeleteLoading(false)
    }
  }

  const handleVehicleUpdated = () => {
    refetch()
    setEditingVehicle(null)
  }

  const handleVehicleAdded = () => {
    refetch()
  }

  // Handle column visibility toggle
  const handleColumnToggle = (columnKey: string, checked: boolean) => {
    setVisibleColumns(prev => ({
      ...prev,
      [columnKey]: checked
    }))
  }

  // Reset columns to default visibility
  const resetColumnsToDefault = () => {
    const defaultVisible: Record<string, boolean> = {}
    allColumns.forEach(col => {
      defaultVisible[col.key] = col.defaultVisible
    })
    setVisibleColumns(defaultVisible)
  }

  // Get filtered columns for rendering
  const getVisibleColumns = () => {
    return allColumns.filter(col => visibleColumns[col.key])
  }

  // Render cell content based on column key
  const renderCellContent = (vehicle: any, columnKey: string) => {
    switch (columnKey) {
      case 'plate_number':
        return <span className="font-medium">{vehicle.plate_number}</span>
      case 'vehicle_type':
        return vehicle.vehicle_type || "-"
      case 'service_type':
        return vehicle.service_type || "-"
      case 'fuel_type':
        return vehicle.fuel_type || "-"
      case 'year':
        return vehicle.year || "-"
      case 'current_km':
        return vehicle.current_km?.toLocaleString() || "0"
      case 'status':
        return getStatusBadge(vehicle.status)
      case 'next_maintenance_km':
        return vehicle.next_maintenance_km?.toLocaleString() || "-"
      default:
        return vehicle[columnKey] || "-"
    }
  }

  // Handle advanced filter changes
  const handleAdvancedFilterChange = (key: string, value: string) => {
    setAdvancedFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  // Apply all filters
  const filteredVehicles = vehicles.filter(vehicle => {
    // Basic search filter
    const matchesSearch = 
      !searchTerm || 
      vehicle.plate_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (vehicle.vehicle_type && vehicle.vehicle_type.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (vehicle.service_type && vehicle.service_type.toLowerCase().includes(searchTerm.toLowerCase()))
    
    // Status filter
    const matchesStatus = 
      statusFilter === "all" || 
      vehicle.status === statusFilter

    // Advanced filters
    const matchesVehicleType = 
      advancedFilters.vehicleType === "__all__" || !advancedFilters.vehicleType ||
      vehicle.vehicle_type === advancedFilters.vehicleType
    
    const matchesServiceType = 
      advancedFilters.serviceType === "__all__" || !advancedFilters.serviceType ||
      vehicle.service_type === advancedFilters.serviceType
    
    const matchesLicenseType = 
      advancedFilters.licenseType === "__all__" || !advancedFilters.licenseType ||
      vehicle.license_type === advancedFilters.licenseType
    
    const matchesVehicleFeatures = 
      advancedFilters.vehicleFeatures === "__all__" || !advancedFilters.vehicleFeatures ||
      vehicle.vehicle_features === advancedFilters.vehicleFeatures
    
    const matchesPermits = 
      advancedFilters.permits === "__all__" || !advancedFilters.permits ||
      vehicle.permits === advancedFilters.permits
    
    const matchesBranch = 
      advancedFilters.branchId === "__all__" || !advancedFilters.branchId || 
      vehicle.branch_id === advancedFilters.branchId

    return matchesSearch && matchesStatus && matchesVehicleType && matchesServiceType && 
           matchesLicenseType && matchesVehicleFeatures && matchesPermits && matchesBranch
  })

  const visibleColumnsList = getVisibleColumns()

  // Reset advanced filters
  const resetAdvancedFilters = () => {
    setAdvancedFilters({
      vehicleType: "__all__",
      serviceType: "__all__",
      licenseType: "__all__",
      vehicleFeatures: "__all__",
      permits: "__all__",
      branchId: "__all__"
    })
    setSearchTerm("")
    setStatusFilter("all")
  }

  const hasActiveFilters = searchTerm || statusFilter !== "all" || 
    (advancedFilters.vehicleType && advancedFilters.vehicleType !== "__all__") || 
    (advancedFilters.serviceType && advancedFilters.serviceType !== "__all__") || 
    (advancedFilters.licenseType && advancedFilters.licenseType !== "__all__") ||
    (advancedFilters.vehicleFeatures && advancedFilters.vehicleFeatures !== "__all__") || 
    (advancedFilters.permits && advancedFilters.permits !== "__all__") || 
    (advancedFilters.branchId && advancedFilters.branchId !== "__all__")

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{language === "ar" ? "المركبات" : "Vehicles"}</h1>
          <p className="text-muted-foreground">
            {language === "ar" ? "إدارة وصيانة أسطول المركبات" : "Manage and maintain your vehicle fleet"}
          </p>
        </div>
        <div>
          <AddVehicleDialog 
            language={language} 
            onVehicleAdded={handleVehicleAdded}
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle>{language === "ar" ? "قائمة المركبات" : "Vehicle List"}</CardTitle>
              <CardDescription>
                {language === "ar" ? "إدارة جميع المركبات في الأسطول" : "Manage all vehicles in your fleet"}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                {language === "ar" ? "الفلاتر" : "Filters"}
              </Button>
              <Dialog open={manageColumnsOpen} onOpenChange={setManageColumnsOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <Settings2 className="h-4 w-4" />
                    {language === "ar" ? "إدارة الأعمدة" : "Manage Columns"}
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>{language === "ar" ? "إدارة الأعمدة" : "Manage Columns"}</DialogTitle>
                    <DialogDescription>
                      {language === "ar" ? "اختر الأعمدة التي تريد إظهارها في الجدول" : "Select which columns to show in the table"}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    {allColumns.map((column) => (
                      <div key={column.key} className="flex items-center space-x-2">
                        <Checkbox
                          id={`column-${column.key}`}
                          checked={visibleColumns[column.key] || false}
                          onCheckedChange={(checked) => handleColumnToggle(column.key, !!checked)}
                        />
                        <Label htmlFor={`column-${column.key}`} className={language === "ar" ? "mr-2" : ""}>
                          {column.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={resetColumnsToDefault}>
                      {language === "ar" ? "إعادة تعيين إلى الافتراضي" : "Reset to Default"}
                    </Button>
                    <Button onClick={() => setManageColumnsOpen(false)}>
                      {language === "ar" ? "إغلاق" : "Close"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
          
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4 p-4 bg-muted rounded-lg">
              <div className="space-y-2">
                <Label htmlFor="search">
                  {language === "ar" ? "البحث" : "Search"}
                </Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder={language === "ar" ? "رقم اللوحة أو النوع..." : "Plate or type..."}
                    className="pl-10"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status">
                  {language === "ar" ? "الحالة" : "Status"}
                </Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر الحالة" : "Select status"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{language === "ar" ? "جميع الحالات" : "All Statuses"}</SelectItem>
                    <SelectItem value="active">{language === "ar" ? "نشط" : "Active"}</SelectItem>
                    <SelectItem value="maintenance">{language === "ar" ? "في الصيانة" : "In Maintenance"}</SelectItem>
                    <SelectItem value="out_of_service">{language === "ar" ? "خارج الخدمة" : "Out of Service"}</SelectItem>
                    <SelectItem value="retired">{language === "ar" ? "متقاعد" : "Retired"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="vehicle-type">
                  {language === "ar" ? "نوع المركبة" : "Vehicle Type"}
                </Label>
                <Select value={advancedFilters.vehicleType} onValueChange={(value) => handleAdvancedFilterChange("vehicleType", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر نوع المركبة" : "Select vehicle type"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="__all__">{language === "ar" ? "جميع الأنواع" : "All Types"}</SelectItem>
                    {uniqueVehicleTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="service-type">
                  {language === "ar" ? "نوع الخدمة" : "Service Type"}
                </Label>
                <Select value={advancedFilters.serviceType} onValueChange={(value) => handleAdvancedFilterChange("serviceType", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر نوع الخدمة" : "Select service type"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="__all__">{language === "ar" ? "جميع الأنواع" : "All Types"}</SelectItem>
                    {uniqueServiceTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="license-type">
                  {language === "ar" ? "نوع الترخيص" : "License Type"}
                </Label>
                <Select value={advancedFilters.licenseType} onValueChange={(value) => handleAdvancedFilterChange("licenseType", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر نوع الترخيص" : "Select license type"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="__all__">{language === "ar" ? "جميع الأنواع" : "All Types"}</SelectItem>
                    {uniqueLicenseTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="vehicle-features">
                  {language === "ar" ? "تفاصيل المركبة" : "Vehicle Features"}
                </Label>
                <Select value={advancedFilters.vehicleFeatures} onValueChange={(value) => handleAdvancedFilterChange("vehicleFeatures", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر تفاصيل المركبة" : "Select vehicle features"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="__all__">{language === "ar" ? "جميع التفاصيل" : "All Features"}</SelectItem>
                    {uniqueVehicleFeatures.map((feature) => (
                      <SelectItem key={feature} value={feature}>
                        {feature}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="permits">
                  {language === "ar" ? "التصاريح" : "Permits"}
                </Label>
                <Select value={advancedFilters.permits} onValueChange={(value) => handleAdvancedFilterChange("permits", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر التصاريح" : "Select permits"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="__all__">{language === "ar" ? "جميع التصاريح" : "All Permits"}</SelectItem>
                    {uniquePermits.map((permit) => (
                      <SelectItem key={permit} value={permit}>
                        {permit}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="branch">
                  {language === "ar" ? "الفرع" : "Branch"}
                </Label>
                <Select value={advancedFilters.branchId} onValueChange={(value) => handleAdvancedFilterChange("branchId", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر الفرع" : "Select branch"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="__all__">{language === "ar" ? "جميع الفروع" : "All Branches"}</SelectItem>
                    {branches.map((branch) => (
                      <SelectItem key={branch.id} value={branch.id}>
                        {branch.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {hasActiveFilters && (
                <div className="lg:col-span-4 flex justify-end">
                  <Button variant="ghost" onClick={resetAdvancedFilters}>
                    {language === "ar" ? "مسح الفلاتر" : "Clear Filters"}
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardHeader>
        <CardContent>
          {/* Vehicles Table */}
          {filteredVehicles.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Car className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>{language === "ar" ? "لا توجد مركبات" : "No vehicles found"}</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  {visibleColumnsList.map((column) => (
                    <TableHead key={column.key}>{column.label}</TableHead>
                  ))}
                  <TableHead>{language === "ar" ? "الإجراءات" : "Actions"}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredVehicles.map((vehicle) => (
                  <TableRow key={vehicle.id}>
                    {visibleColumnsList.map((column) => (
                      <TableCell key={`${vehicle.id}-${column.key}`}>
                        {renderCellContent(vehicle, column.key)}
                      </TableCell>
                    ))}
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewProfile(vehicle)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditVehicle(vehicle)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteVehicle(vehicle)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit Vehicle Dialog */}
      {editingVehicle && (
        <EditVehicleDialog
          vehicle={editingVehicle}
          open={!!editingVehicle}
          onClose={() => setEditingVehicle(null)}
          onVehicleUpdated={handleVehicleUpdated}
          language={language}
        />
      )}

      {/* Vehicle Profile Dialog */}
      {profileVehicle && (
        <VehicleProfileDialog
          vehicle={profileVehicle}
          open={!!profileVehicle}
          onClose={() => setProfileVehicle(null)}
          language={language}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingVehicle} onOpenChange={(open) => !open && setDeletingVehicle(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{language === "ar" ? "تأكيد الحذف" : "Confirm Delete"}</AlertDialogTitle>
            <AlertDialogDescription>
              {language === "ar" 
                ? `هل أنت متأكد من أنك تريد حذف المركبة ${deletingVehicle?.plate_number}؟ هذا الإجراء لا يمكن التراجع عنه.`
                : `Are you sure you want to delete vehicle ${deletingVehicle?.plate_number}? This action cannot be undone.`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteLoading}>
              {language === "ar" ? "إلغاء" : "Cancel"}
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDeleteVehicle} 
              disabled={deleteLoading}
              className="bg-destructive hover:bg-destructive/90"
            >
              {deleteLoading ? (
                <>{language === "ar" ? "جاري الحذف..." : "Deleting..."}</>
              ) : (
                <>{language === "ar" ? "حذف" : "Delete"}</>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}