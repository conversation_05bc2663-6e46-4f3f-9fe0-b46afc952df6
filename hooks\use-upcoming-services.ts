import { useState, useEffect } from "react"
import { supabase } from "@/lib/supabase"

export interface UpcomingService {
  vehicle_id: string
  plate_number: string
  service_type: string
  service_type_ar: string
  trigger_value: number | null
  current_value: number | null
  remaining_km: number | null
  due_date: string | null
  reminder_days: number | null
  branch_id: string | null
}

export interface UpcomingServicesFilter {
  plateNumber?: string
  serviceType?: string
  priority?: 'urgent' | 'important' | 'normal'
  dateRange?: {
    from?: string
    to?: string
  }
}

export function useUpcomingServices(limit?: number, filter?: UpcomingServicesFilter) {
  const [data, setData] = useState<UpcomingService[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchUpcomingServices = async () => {
      try {
        setLoading(true)
        setError(null)
        
        let query = supabase
          .from('upcoming_services')
          .select('*')
          .order('due_date', { ascending: true, nullsFirst: false })
          .order('remaining_km', { ascending: true, nullsFirst: false })
        
        // Apply plate number filter
        if (filter?.plateNumber) {
          query = query.ilike('plate_number', `%${filter.plateNumber}%`)
        }
        
        // Apply service type filter
        if (filter?.serviceType) {
          query = query.eq('service_type', filter.serviceType)
        }
        
        // Apply date range filter
        if (filter?.dateRange?.from) {
          query = query.gte('due_date', filter.dateRange.from)
        }
        if (filter?.dateRange?.to) {
          query = query.lte('due_date', filter.dateRange.to)
        }
        
        if (limit) {
          query = query.limit(limit)
        }
        
        const { data, error } = await query

        if (error) {
          throw error
        }

        // Apply priority filter after fetching data
        let filteredData = data || []
        if (filter?.priority) {
          filteredData = filteredData.filter(service => {
            const daysUntilDue = service.due_date ? 
              Math.ceil((new Date(service.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 
              null
            const remainingKm = service.remaining_km
            
            switch (filter.priority) {
              case 'urgent':
                return (daysUntilDue !== null && daysUntilDue <= 7) || (remainingKm !== null && remainingKm <= 500)
              case 'important':
                return (daysUntilDue !== null && daysUntilDue <= 30) || (remainingKm !== null && remainingKm <= 1000)
              case 'normal':
                return (daysUntilDue === null || daysUntilDue > 30) && (remainingKm === null || remainingKm > 1000)
              default:
                return true
            }
          })
        }

        setData(filteredData)
      } catch (err) {
        console.error('Error fetching upcoming services:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    fetchUpcomingServices()
  }, [limit, filter])

  return { data, loading, error }
}