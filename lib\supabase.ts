import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://tilywrugwehpbeljwmsx.supabase.co'
const supabaseAnonKey =
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRpbHl3cnVnd2VocGJlbGp3bXN4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU3MDUwNTUsImV4cCI6MjA3MTI4MTA1NX0.N_GGEnW5tH0E8lhSkCktwtjzWbbhrNO7kryO5c04wN0'

export function createServerClient() {
  return createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      flowType: 'pkce',
    },
    global: {
      headers: {
        'x-application-name': 'fleet-management-dashboard',
      },
    },
  })
}

// For backward compatibility, export a default client
export const supabase = createServerClient()

// Database types matching your actual schema
export interface Brand {
  id: string
  name: string
  description?: string
  logo_url?: string
  created_at: string
}

export interface Branch {
  id: string
  brand_id?: string
  name: string
  location?: string
  manager_name?: string
  contact_phone?: string
  created_at: string
}

export interface User {
  id: string
  full_name: string
  email: string
  password_hash?: string
  role:
    | "system_super_admin"
    | "admin"
    | "fleet_manager"
    | "branch_manager"
    | "driver"
    | "mechanic"
    | "operator"
    | "viewer"
  job_title?: string
  branch_id?: string
  user_status: "active" | "inactive" | "suspended" | "pending"
  last_login?: string
  created_at: string
  updated_at: string
}

export interface Vehicle {
  id: string
  plate_number: string
  color?: string
  vehicle_type?: string
  service_type?: string
  fuel_type?: "gasoline_95" | "gasoline_92" | "diesel" | "electric"
  year?: number
  vin?: string
  current_km?: number
  last_maintenance_km?: number
  next_maintenance_km?: number
  next_maintenance_date?: string
  last_tire_change_km?: number
  next_tire_change_km?: number
  next_tire_change_date?: string
  insurance_expiry?: string
  license_expiry?: string
  license_type?: string
  vehicle_features?: string
  permits?: string
  status: "active" | "maintenance" | "out_of_service" | "retired" | "inspection"
  branch_id?: string
  created_at: string
}

export interface Driver {
  id: string
  full_name: string
  national_id?: string
  license_number: string
  license_expiry: string
  phone: string
  performance_score?: number
  branch_id?: string
  created_at: string
}

export interface VehicleAssignment {
  id: string
  vehicle_id: string
  driver_id: string
  start_date: string
  end_date?: string
  assignment_notes?: string
  assigned_by?: string
  created_at: string
}

export interface Maintenance {
  id: string
  vehicle_id: string
  service_date: string
  service_type: "preventive" | "corrective" | "emergency" | "inspection"
  description?: string
  current_km?: number
  severity?: "low" | "medium" | "high" | "critical"
  vendor_id?: string
  labor_cost_egp?: number
  parts_cost_egp?: number
  total_cost_egp?: number
  notes?: string
  created_at: string
}

export interface FuelLog {
  id: string
  vehicle_id: string
  driver_id?: string
  fuel_date: string
  liters: number
  cost_egp: number
  fuel_station?: string
  km_at_refill?: number
  created_at: string
}

export interface Alert {
  id: string
  vehicle_id?: string
  driver_id?: string
  alert_type: string
  alert_type_ar?: string
  severity: "low" | "medium" | "high" | "critical"
  message: string
  message_ar?: string
  acknowledged: boolean
  acknowledged_by?: string
  acknowledged_at?: string
  resolved: boolean
  resolved_by?: string
  resolved_at?: string
  metadata?: any
  created_at: string
}

// Materialized View Types
export interface DashboardOverviewAll {
  dashboard_level: string
  filter_id?: string
  filter_name: string
  total_vehicles: number
  active_vehicles: number
  monthly_operating_cost: number
  fuel_efficiency_km_per_liter: number
  cost_per_kilometer: number
  fleet_utilization_rate: number
  driver_performance: number
  overdue_maintenance: number
  insurance_claims_pending: number
  maintenance_compliance: number
  roi_percentage: number
  last_updated: string
}

export interface FleetOverview {
  fleet_type: string
  total_vehicles: number
  active_vehicles: number
  inactive_vehicles: number
  maintenance_vehicles: number
  out_of_service_vehicles: number
  current_month_fuel_liters?: number
  current_month_fuel_cost?: number
  previous_month_fuel_liters?: number
  vehicles_with_scheduled_maintenance: number
  overdue_maintenance_vehicles: number
  maintenance_compliance_percentage: number
  avg_driver_performance?: number
  excellent_drivers: number
  good_drivers: number
  poor_drivers: number
  total_drivers: number
  monthly_fuel_costs?: number
  monthly_maintenance_costs?: number
  monthly_other_expenses?: number
  fleet_fuel_efficiency_km_per_liter: number
  total_distance_traveled?: number
  avg_vehicle_mileage?: number
  last_updated: string
}

export interface UpcomingService {
  vehicle_id: string
  vehicle_type?: string
  plate_number: string
  current_km?: number
  service_type: string
  due_date?: string
  due_km?: number
  days_until_due?: number
  km_until_due?: number
  priority: string
  vehicle_status: string
  last_updated: string
}

export interface MaintenanceStatistics {
  stats_type: string
  total_records: number
  total_cost_ytd: number
  average_cost: number
  service_types_count: number
  unique_vehicles: number
  parts_cost_ytd: number
  labor_cost_ytd: number
  critical_records: number
  service_centers_count: number
  next_service_due: number
  last_maintenance_date?: string
  avg_days_between_maintenance: number
  most_common_service_type?: string
  last_updated: string
}
