"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { User2, Search, Filter, Edit, Trash2, Eye } from "lucide-react"
import { useUsers } from "@/hooks/use-fleet-data"
import { LoadingSpinner } from "@/components/loading-spinner"
import { ErrorDisplay } from "@/components/error-display"
import { AddUserDialog } from "@/components/forms/add-user-dialog"
import { EditUserDialog } from "@/components/forms/edit-user-dialog"
import { User } from "@/lib/supabase"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { supabase } from "@/lib/supabase"
import { useToast } from "@/hooks/use-toast"

interface UserManagementProps {
  language: "ar" | "en"
}

export function UserManagement({ language }: UserManagementProps) {
  const { data: users, loading, error, refetch } = useUsers(100)
  const [searchTerm, setSearchTerm] = useState("")
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const { toast } = useToast()

  if (loading) {
    return <LoadingSpinner className="h-64" />
  }

  if (error) {
    return <ErrorDisplay error={error} />
  }

  const filteredUsers = users.filter((user) =>
    user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.job_title && user.job_title.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      system_super_admin: { variant: "destructive" as const, label: language === "ar" ? "مدير النظام الأعلى" : "System Super Admin" },
      admin: { variant: "destructive" as const, label: language === "ar" ? "مدير" : "Admin" },
      fleet_manager: { variant: "default" as const, label: language === "ar" ? "مدير أسطول" : "Fleet Manager" },
      branch_manager: { variant: "default" as const, label: language === "ar" ? "مدير فرع" : "Branch Manager" },
      driver: { variant: "secondary" as const, label: language === "ar" ? "سائق" : "Driver" },
      mechanic: { variant: "secondary" as const, label: language === "ar" ? "ميكانيكي" : "Mechanic" },
      operator: { variant: "outline" as const, label: language === "ar" ? "مشغل" : "Operator" },
      viewer: { variant: "outline" as const, label: language === "ar" ? "مشاهد" : "Viewer" }
    }

    const config = roleConfig[role as keyof typeof roleConfig] || { variant: "outline" as const, label: role }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { variant: "default" as const, label: language === "ar" ? "نشط" : "Active" },
      inactive: { variant: "secondary" as const, label: language === "ar" ? "غير نشط" : "Inactive" },
      suspended: { variant: "destructive" as const, label: language === "ar" ? "موقوف" : "Suspended" },
      pending: { variant: "outline" as const, label: language === "ar" ? "في الانتظار" : "Pending" }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || { variant: "outline" as const, label: status }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const handleDeleteUser = async (userId: string) => {
    try {
      const { error } = await supabase
        .from("users")
        .delete()
        .eq("id", userId)

      if (error) throw error

      toast({
        title: language === "ar" ? "تم حذف المستخدم" : "User Deleted",
        description: language === "ar" ? "تم حذف المستخدم بنجاح" : "User has been deleted successfully",
      })

      refetch()
    } catch (err) {
      console.error("Error deleting user:", err)
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description: language === "ar" ? "حدث خطأ أثناء حذف المستخدم" : "An error occurred while deleting the user",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(language === "ar" ? "ar-EG" : "en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {language === "ar" ? "إدارة المستخدمين" : "User Management"}
          </h1>
          <p className="text-muted-foreground">
            {language === "ar" ? "إدارة وتتبع جميع المستخدمين" : "Manage and track all users"}
          </p>
        </div>
        <AddUserDialog language={language} onUserAdded={refetch} />
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User2 className="h-5 w-5" />
            {language === "ar" ? "قائمة المستخدمين" : "User List"}
          </CardTitle>
          <CardDescription>
            {language === "ar" ? "جميع المستخدمين في النظام" : "All users in the system"}
          </CardDescription>
        </CardHeader>

        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="flex-1">
              <Label htmlFor="search">{language === "ar" ? "البحث" : "Search"}</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder={language === "ar" ? "البحث في المستخدمين..." : "Search users..."}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              {language === "ar" ? "تصفية" : "Filter"}
            </Button>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === "ar" ? "الاسم الكامل" : "Full Name"}</TableHead>
                <TableHead>{language === "ar" ? "البريد الإلكتروني" : "Email"}</TableHead>
                <TableHead>{language === "ar" ? "الدور" : "Role"}</TableHead>
                <TableHead>{language === "ar" ? "المسمى الوظيفي" : "Job Title"}</TableHead>
                <TableHead>{language === "ar" ? "الحالة" : "Status"}</TableHead>
                <TableHead>{language === "ar" ? "آخر دخول" : "Last Login"}</TableHead>
                <TableHead>{language === "ar" ? "تاريخ الإنشاء" : "Created At"}</TableHead>
                <TableHead className="text-center">{language === "ar" ? "الإجراءات" : "Actions"}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                    {language === "ar" ? "لا توجد مستخدمين" : "No users found"}
                  </TableCell>
                </TableRow>
              ) : (
                filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">{user.full_name}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>{getRoleBadge(user.role)}</TableCell>
                    <TableCell>{user.job_title || "-"}</TableCell>
                    <TableCell>{getStatusBadge(user.user_status)}</TableCell>
                    <TableCell>
                      {user.last_login ? formatDate(user.last_login) : language === "ar" ? "لم يسجل دخول" : "Never"}
                    </TableCell>
                    <TableCell>{formatDate(user.created_at)}</TableCell>
                    <TableCell>
                      <div className="flex items-center justify-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingUser(user)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="sm" className="text-destructive hover:text-destructive">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                {language === "ar" ? "تأكيد الحذف" : "Confirm Deletion"}
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                {language === "ar" 
                                  ? `هل أنت متأكد من حذف المستخدم "${user.full_name}"؟ هذا الإجراء لا يمكن التراجع عنه.`
                                  : `Are you sure you want to delete user "${user.full_name}"? This action cannot be undone.`
                                }
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>
                                {language === "ar" ? "إلغاء" : "Cancel"}
                              </AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteUser(user.id)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                {language === "ar" ? "حذف" : "Delete"}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* Users Count */}
          <div className="flex items-center justify-between text-sm text-muted-foreground mt-4">
            <span>
              {language === "ar" 
                ? `عرض ${filteredUsers.length} من ${users.length} مستخدم`
                : `Showing ${filteredUsers.length} of ${users.length} users`
              }
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Edit User Dialog */}
      {editingUser && (
        <EditUserDialog
          user={editingUser}
          language={language}
          open={!!editingUser}
          onClose={() => setEditingUser(null)}
          onUserUpdated={() => {
            refetch()
            setEditingUser(null)
          }}
        />
      )}
    </div>
  )
}