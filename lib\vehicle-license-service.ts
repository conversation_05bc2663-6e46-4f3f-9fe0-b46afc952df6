/**
 * Vehicle License Image Management Service
 * 
 * خدمة إدارة صور رخص المركبات
 * Comprehensive service for managing vehicle license images with Supabase Storage
 * 
 * Features:
 * - Upload license images to Supabase Storage
 * - Generate public URLs for images
 * - Update vehicle records with image URLs
 * - Clean up old images when replacing
 * - Browser-compatible implementation
 * 
 * Author: AI Assistant
 * Date: 2025-08-26
 */

import { createClient } from '@/lib/supabase-browser';

// Types for better TypeScript support
export interface UploadResult {
  success: boolean;
  imageUrl?: string;
  error?: string;
}

export interface VehicleLicenseData {
  vehicleId: string;
  frontImage?: File;
  backImage?: File;
}

// Type for vehicle license image data from database
export interface VehicleLicenseRecord {
  license_front_image?: string | null;
  license_back_image?: string | null;
}

// Type for single license image column selection
export interface VehicleSingleLicenseColumn {
  license_front_image?: string | null;
}

export interface VehicleBackLicenseColumn {
  license_back_image?: string | null;
}

export type LicenseSide = 'front' | 'back';

/**
 * Vehicle License Image Service Class
 * Handles all license image operations with Supabase Storage
 */
export class VehicleLicenseService {
  private supabase;
  private readonly BUCKET_NAME = 'vehicle-licenses';
  
  constructor() {
    this.supabase = createClient();
  }

  /**
   * Upload a license image to Supabase Storage and update vehicle record
   * رفع صورة رخصة إلى Supabase Storage وتحديث سجل المركبة
   * 
   * @param file - Image file to upload
   * @param vehicleId - Vehicle ID to associate with the image
   * @param side - 'front' or 'back' license side
   * @returns Promise with upload result including public URL
   */
  async uploadLicenseImage(file: File, vehicleId: string, side: LicenseSide): Promise<UploadResult> {
    try {
      // Debug: Check authentication status
      const { data: { user }, error: authError } = await this.supabase.auth.getUser();
      console.log('🔐 Auth Debug Info:', {
        isAuthenticated: !!user,
        userId: user?.id,
        userEmail: user?.email,
        authError: authError?.message
      });

      if (!user) {
        throw new Error('المستخدم غير مصادق - User not authenticated. Please log in first.');
      }

      // Validate inputs
      if (!file) {
        throw new Error('ملف الصورة مطلوب - Image file is required');
      }

      if (!vehicleId) {
        throw new Error('معرف المركبة مطلوب - Vehicle ID is required');
      }

      if (!['front', 'back'].includes(side)) {
        throw new Error('جانب الرخصة يجب أن يكون front أو back - License side must be front or back');
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        throw new Error('نوع الملف غير مدعوم. يرجى استخدام JPEG, PNG أو WebP - Unsupported file type. Please use JPEG, PNG or WebP');
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        throw new Error('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت - File too large. Maximum size is 5MB');
      }

      console.log(`🚀 Starting upload for vehicle ${vehicleId}, ${side} side, file size: ${file.size} bytes`);

      // Check and create bucket if needed
      await this.ensureBucketExists();

      // Remove old image if exists
      await this.removeOldImage(vehicleId, side);

      // Generate unique file path
      const timestamp = Date.now();
      const fileExtension = file.name.split('.').pop() || 'jpg';
      const fileName = `${side}_${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
      const filePath = `vehicles/${vehicleId}/${fileName}`;

      console.log(`📁 Uploading to path: ${filePath}`);

      // Debug: Log bucket and file information
      console.log('📁 Upload Debug Info:', {
        bucketName: this.BUCKET_NAME,
        filePath: filePath,
        fileSize: file.size,
        fileType: file.type,
        vehicleId: vehicleId,
        side: side,
        userId: user.id
      });

      // Upload file to Supabase Storage
      const { data: uploadData, error: uploadError } = await this.supabase.storage
        .from(this.BUCKET_NAME)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false // Don't overwrite existing files
        });

      if (uploadError) {
        console.error('❌ Upload failed:', uploadError);
        throw new Error(`فشل رفع الملف: ${uploadError.message} - Upload failed: ${uploadError.message}`);
      }

      console.log('✅ Upload successful:', uploadData);

      // Get public URL
      const { data: urlData } = this.supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(filePath);

      if (!urlData?.publicUrl) {
        throw new Error('فشل في الحصول على رابط الصورة - Failed to get image URL');
      }

      const publicUrl = urlData.publicUrl;
      console.log(`🔗 Public URL generated: ${publicUrl}`);

      // Update vehicle record with new image URL
      const updateResult = await this.updateVehicleImageUrl(vehicleId, side, publicUrl);
      
      if (!updateResult.success) {
        // If database update fails, try to clean up the uploaded file
        await this.cleanupUploadedFile(filePath);
        throw new Error(updateResult.error || 'فشل تحديث سجل المركبة - Failed to update vehicle record');
      }

      console.log(`✅ Vehicle record updated successfully for ${side} side`);

      return {
        success: true,
        imageUrl: publicUrl
      };

    } catch (error) {
      console.error('❌ License image upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'حدث خطأ غير معروف - Unknown error occurred'
      };
    }
  }

  /**
   * Update vehicle record with new image URL
   * تحديث سجل المركبة برابط الصورة الجديد
   */
  private async updateVehicleImageUrl(vehicleId: string, side: LicenseSide, imageUrl: string): Promise<UploadResult> {
    try {
      const columnName = side === 'front' ? 'license_front_image' : 'license_back_image';
      
      const { error } = await this.supabase
        .from('vehicles')
        .update({ [columnName]: imageUrl })
        .eq('id', vehicleId);

      if (error) {
        console.error(`❌ Failed to update ${columnName}:`, error);
        throw new Error(`فشل تحديث ${side === 'front' ? 'الصورة الأمامية' : 'الصورة الخلفية'} - Failed to update ${side} image`);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Database update failed'
      };
    }
  }

  /**
   * Remove old image from storage before uploading new one
   * إزالة الصورة القديمة من التخزين قبل رفع صورة جديدة
   */
  private async removeOldImage(vehicleId: string, side: LicenseSide): Promise<void> {
    try {
      // Get current image URL from database
      const columnName = side === 'front' ? 'license_front_image' : 'license_back_image';
      
      const { data: vehicle, error } = await this.supabase
        .from('vehicles')
        .select(columnName)
        .eq('id', vehicleId)
        .single();

      if (error || !vehicle) {
        console.log(`ℹ️ No existing ${side} image to remove for vehicle ${vehicleId}`);
        return;
      }

      // Type-safe property access
      const imageUrl = side === 'front' 
        ? (vehicle as { license_front_image?: string }).license_front_image
        : (vehicle as { license_back_image?: string }).license_back_image;

      if (!imageUrl) {
        console.log(`ℹ️ No existing ${side} image URL found for vehicle ${vehicleId}`);
        return;
      }

      // Extract file path from URL
      try {
        // Validate imageUrl before creating URL object
        if (typeof imageUrl === 'string' && imageUrl.trim() !== '') {
          const urlPath = new URL(imageUrl).pathname;
          const filePath = urlPath.split(`/${this.BUCKET_NAME}/`)[1];

          if (filePath) {
            console.log(`🗑️ Removing old ${side} image: ${filePath}`);
            
            const { error: deleteError } = await this.supabase.storage
              .from(this.BUCKET_NAME)
              .remove([filePath]);

            if (deleteError) {
              console.warn(`⚠️ Could not delete old image: ${deleteError.message}`);
            } else {
              console.log(`✅ Old ${side} image removed successfully`);
            }
          }
        } else {
          console.log(`ℹ️ Skipping image removal - no valid URL provided for ${side} image`);
        }
      } catch (urlError) {
        console.warn(`⚠️ Invalid image URL format: ${imageUrl}`, urlError);
      }
    } catch (error) {
      console.warn(`⚠️ Error removing old image:`, error);
      // Don't throw error here - old image cleanup is not critical
    }
  }

  /**
   * Clean up uploaded file if database update fails
   * تنظيف الملف المرفوع في حال فشل تحديث قاعدة البيانات
   */
  private async cleanupUploadedFile(filePath: string): Promise<void> {
    try {
      console.log(`🧹 Cleaning up uploaded file: ${filePath}`);
      
      const { error } = await this.supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath]);

      if (error) {
        console.warn(`⚠️ Failed to cleanup uploaded file: ${error.message}`);
      } else {
        console.log(`✅ Uploaded file cleaned up successfully`);
      }
    } catch (error) {
      console.warn(`⚠️ Error during file cleanup:`, error);
    }
  }

  /**
   * Ensure the vehicle-licenses bucket exists
   * التأكد من وجود bucket الرخص
   */
  private async ensureBucketExists(): Promise<void> {
    try {
      // Check if bucket exists
      const { data: buckets, error: listError } = await this.supabase.storage.listBuckets();
      
      if (listError) {
        console.warn('⚠️ Could not list buckets:', listError.message);
        return; // Assume bucket exists
      }

      const bucketExists = buckets?.some(bucket => bucket.name === this.BUCKET_NAME);
      
      if (!bucketExists) {
        console.log(`📦 Creating bucket: ${this.BUCKET_NAME}`);
        
        const { error: createError } = await this.supabase.storage.createBucket(this.BUCKET_NAME, {
          public: true,
          allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
          fileSizeLimit: 5242880 // 5MB
        });

        if (createError) {
          console.warn(`⚠️ Could not create bucket: ${createError.message}`);
        } else {
          console.log(`✅ Bucket ${this.BUCKET_NAME} created successfully`);
        }
      }
    } catch (error) {
      console.warn('⚠️ Error checking bucket:', error);
      // Continue anyway - bucket might exist
    }
  }

  /**
   * Get current license images for a vehicle
   * الحصول على صور الرخصة الحالية للمركبة
   */
  async getVehicleLicenseImages(vehicleId: string): Promise<{
    frontImage?: string;
    backImage?: string;
    error?: string;
  }> {
    try {
      const { data: vehicle, error } = await this.supabase
        .from('vehicles')
        .select('license_front_image, license_back_image')
        .eq('id', vehicleId)
        .single();

      if (error) {
        throw new Error(`فشل في الحصول على صور الرخصة - Failed to get license images: ${error.message}`);
      }

      // Type-safe access to vehicle license data
      const licenseData = vehicle as VehicleLicenseRecord;

      return {
        frontImage: licenseData?.license_front_image || undefined,
        backImage: licenseData?.license_back_image || undefined
      };
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Upload multiple license images at once
   * رفع عدة صور رخصة في نفس الوقت
   */
  async uploadMultipleLicenseImages(vehicleId: string, images: {
    front?: File;
    back?: File;
  }): Promise<{
    success: boolean;
    results: {
      front?: UploadResult;
      back?: UploadResult;
    };
    error?: string;
  }> {
    try {
      const results: { front?: UploadResult; back?: UploadResult } = {};
      let hasError = false;

      // Upload front image if provided
      if (images.front) {
        console.log('📸 Uploading front license image...');
        results.front = await this.uploadLicenseImage(images.front, vehicleId, 'front');
        if (!results.front.success) {
          hasError = true;
        }
      }

      // Upload back image if provided
      if (images.back) {
        console.log('📸 Uploading back license image...');
        results.back = await this.uploadLicenseImage(images.back, vehicleId, 'back');
        if (!results.back.success) {
          hasError = true;
        }
      }

      return {
        success: !hasError,
        results,
        error: hasError ? 'فشل في رفع بعض الصور - Some images failed to upload' : undefined
      };
    } catch (error) {
      return {
        success: false,
        results: {},
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Create singleton instance for easy import
export const vehicleLicenseService = new VehicleLicenseService();

// Utility functions for easy use
export async function uploadLicenseImage(file: File, vehicleId: string, side: LicenseSide): Promise<UploadResult> {
  return vehicleLicenseService.uploadLicenseImage(file, vehicleId, side);
}

export async function getVehicleLicenseImages(vehicleId: string) {
  return vehicleLicenseService.getVehicleLicenseImages(vehicleId);
}

export async function uploadMultipleLicenseImages(vehicleId: string, images: { front?: File; back?: File }) {
  return vehicleLicenseService.uploadMultipleLicenseImages(vehicleId, images);
}