"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { MapPin, Navigation, Clock, Zap } from "lucide-react"

interface GPSTrackingProps {
  language: "ar" | "en"
}

export function GPSTracking({ language }: GPSTrackingProps) {
  // Mock GPS data
  const mockVehicles = [
    {
      id: "1",
      plate_number: "ABC-123",
      driver: "<PERSON>",
      location: "King Fahd Road, Riyadh",
      speed: 45,
      status: "moving",
      last_update: "2 min ago",
    },
    {
      id: "2",
      plate_number: "XYZ-789",
      driver: "Mohammed Ali",
      location: "Olaya Street, Riyadh",
      speed: 0,
      status: "parked",
      last_update: "5 min ago",
    },
    {
      id: "3",
      plate_number: "DEF-456",
      driver: "<PERSON>",
      location: "Prince Sultan Road, Riyadh",
      speed: 60,
      status: "moving",
      last_update: "1 min ago",
    },
  ]

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      moving: { variant: "default" as const, label: language === "ar" ? "متحرك" : "Moving", icon: Navigation },
      parked: { variant: "secondary" as const, label: language === "ar" ? "متوقف" : "Parked", icon: MapPin },
      idle: { variant: "outline" as const, label: language === "ar" ? "خامل" : "Idle", icon: Clock },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.parked
    const Icon = config.icon

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{language === "ar" ? "تتبع GPS" : "GPS Tracking"}</h2>
          <p className="text-muted-foreground">
            {language === "ar" ? "تتبع المركبات في الوقت الفعلي" : "Real-time vehicle tracking"}
          </p>
        </div>
        <Button>
          <Zap className="mr-2 h-4 w-4" />
          {language === "ar" ? "تحديث مباشر" : "Live Update"}
        </Button>
      </div>

      {/* Map Placeholder */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            {language === "ar" ? "خريطة الأسطول" : "Fleet Map"}
          </CardTitle>
          <CardDescription>
            {language === "ar" ? "مواقع المركبات الحالية" : "Current vehicle locations"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">{language === "ar" ? "خريطة GPS التفاعلية" : "Interactive GPS Map"}</p>
              <p className="text-sm text-gray-400 mt-2">
                {language === "ar" ? "سيتم عرض الخريطة هنا" : "Map will be displayed here"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle List */}
      <Card>
        <CardHeader>
          <CardTitle>{language === "ar" ? "المركبات المتتبعة" : "Tracked Vehicles"}</CardTitle>
          <CardDescription>{language === "ar" ? "حالة المركبات الحالية" : "Current vehicle status"}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockVehicles.map((vehicle) => (
              <div key={vehicle.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="flex flex-col">
                    <span className="font-medium">{vehicle.plate_number}</span>
                    <span className="text-sm text-muted-foreground">{vehicle.driver}</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-sm">{vehicle.location}</span>
                    <span className="text-xs text-muted-foreground">
                      {vehicle.speed} {language === "ar" ? "كم/س" : "km/h"} • {vehicle.last_update}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(vehicle.status)}
                  <Button variant="ghost" size="sm">
                    <MapPin className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
