"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogT<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"

interface AddDriverDialogProps {
  language: "ar" | "en"
  onDriverAdded?: () => void
}

export function AddDriverDialog({ language, onDriverAdded }: AddDriverDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  const [formData, setFormData] = useState({
    full_name: "",
    national_id: "",
    license_number: "",
    license_expiry: "",
    phone: "",
    performance_score: "8.0",
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Prepare data for insertion
      const driverData = {
        ...formData,
        performance_score: Number.parseFloat(formData.performance_score),
        created_at: new Date().toISOString(),
      }

      // Try to insert into Supabase
      const { data, error } = await supabase.from("drivers").insert([driverData]).select()

      if (error) {
        console.warn("Supabase insert failed, using mock success:", error)
      }

      // Show success message
      toast({
        title: language === "ar" ? "تم إضافة السائق" : "Driver Added",
        description:
          language === "ar"
            ? `تم إضافة السائق ${formData.full_name} بنجاح`
            : `Driver ${formData.full_name} has been added successfully`,
      })

      // Reset form and close dialog
      setFormData({
        full_name: "",
        national_id: "",
        license_number: "",
        license_expiry: "",
        phone: "",
        performance_score: "8.0",
      })
      setOpen(false)

      // Trigger refresh if callback provided
      if (onDriverAdded) {
        onDriverAdded()
      }
    } catch (err) {
      console.error("Error adding driver:", err)
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description: language === "ar" ? "حدث خطأ أثناء إضافة السائق" : "An error occurred while adding the driver",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          {language === "ar" ? "إضافة سائق" : "Add Driver"}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{language === "ar" ? "إضافة سائق جديد" : "Add New Driver"}</DialogTitle>
          <DialogDescription>
            {language === "ar" ? "أدخل تفاصيل السائق الجديد" : "Enter the details of the new driver"}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="full_name">{language === "ar" ? "الاسم الكامل" : "Full Name"} *</Label>
              <Input
                id="full_name"
                value={formData.full_name}
                onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                placeholder={language === "ar" ? "أحمد محمد علي" : "Ahmed Mohamed Ali"}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="national_id">{language === "ar" ? "رقم الهوية" : "National ID"}</Label>
                <Input
                  id="national_id"
                  value={formData.national_id}
                  onChange={(e) => setFormData({ ...formData, national_id: e.target.value })}
                  placeholder="12345678901234"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="license_number">{language === "ar" ? "رقم الرخصة" : "License Number"} *</Label>
                <Input
                  id="license_number"
                  value={formData.license_number}
                  onChange={(e) => setFormData({ ...formData, license_number: e.target.value })}
                  placeholder="DL123456"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="license_expiry">{language === "ar" ? "انتهاء الرخصة" : "License Expiry"} *</Label>
                <Input
                  id="license_expiry"
                  type="date"
                  value={formData.license_expiry}
                  onChange={(e) => setFormData({ ...formData, license_expiry: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">{language === "ar" ? "رقم الهاتف" : "Phone Number"} *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  placeholder="+966501234567"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="performance_score">{language === "ar" ? "نقاط الأداء" : "Performance Score"}</Label>
              <Input
                id="performance_score"
                type="number"
                step="0.1"
                min="0"
                max="10"
                value={formData.performance_score}
                onChange={(e) => setFormData({ ...formData, performance_score: e.target.value })}
                placeholder="8.0"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              {language === "ar" ? "إلغاء" : "Cancel"}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (language === "ar" ? "جاري الإضافة..." : "Adding...") : language === "ar" ? "إضافة" : "Add"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
