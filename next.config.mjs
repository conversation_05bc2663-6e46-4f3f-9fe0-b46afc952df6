/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Configure for static export only in production
  output: 'export',
  // Disable middleware for static export
  skipMiddlewareUrlNormalize: true,
  skipTrailingSlashRedirect: true,
  // Ensure proper handling of dynamic routes
  trailingSlash: false,
  // Configure for Vercel deployment
  distDir: 'out',
  // Add webpack configuration to resolve WASM hashing issue
  webpack: (config, { isServer }) => {
    // Fix for WASM hashing issue
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: false,
    };
    
    // Ensure proper handling of WASM modules
    config.module.rules.push({
      test: /\.wasm$/,
      type: 'asset/resource',
    });
    
    return config;
  },
}

export default nextConfig