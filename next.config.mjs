/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Remove static export for development
  // output: 'export', // Commented out for development
  // Remove middleware settings that cause issues
  // skipMiddlewareUrlNormalize: true,
  // skipTrailingSlashRedirect: true,
  // Ensure proper handling of dynamic routes
  trailingSlash: false,
  // Use default distDir for development
  // distDir: 'out', // Commented out for development
  // Add webpack configuration to resolve WASM hashing issue
  webpack: (config, { isServer }) => {
    // Fix for WASM hashing issue
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: false,
    };

    // Ensure proper handling of WASM modules
    config.module.rules.push({
      test: /\.wasm$/,
      type: 'asset/resource',
    });

    return config;
  },
}

export default nextConfig