/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Remove static export for production to support Supabase
  // output: 'export', // Commented out - causes issues with Supabase
  // skipMiddlewareUrlNormalize: true,
  // skipTrailingSlashRedirect: true,
  // Ensure proper handling of dynamic routes
  trailingSlash: false,
  // Use default distDir for production
  // distDir: 'out', // Commented out
  // Add webpack configuration to resolve WASM hashing issue
  webpack: (config, { isServer }) => {
    // Fix for WASM hashing issue
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: false,
    };

    // Ensure proper handling of WASM modules
    config.module.rules.push({
      test: /\.wasm$/,
      type: 'asset/resource',
    });

    return config;
  },
}

export default nextConfig