"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ModeToggle } from "@/components/mode-toggle"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Languages } from "lucide-react"
import { useLanguage } from "@/contexts/language-context"
import { getIconMargin, cnWithDirection } from "@/lib/utils"
import { usePathname } from "next/navigation"

export function DashboardHeader() {
  const { language, setLanguage, isRTL } = useLanguage()
  const pathname = usePathname()
  
  // Get the current page name based on the pathname
  const getCurrentPageName = () => {
    switch (pathname) {
      case "/":
        return language === "ar" ? "لوحة التحكم" : "Dashboard"
      case "/alerts":
        return language === "ar" ? "التنبيهات" : "Alerts"
      case "/vehicles":
        return language === "ar" ? "المركبات" : "Vehicles"
      case "/maintenance":
        return language === "ar" ? "الصيانة" : "Maintenance"
      case "/fuel":
        return language === "ar" ? "الوقود" : "Fuel"
      case "/driver":
        return language === "ar" ? "السائق" : "Driver"
      case "/gps":
        return language === "ar" ? "نظام تحديد المواقع" : "GPS"
      case "/financial":
        return language === "ar" ? "المالية" : "Financial"
      case "/reports":
        return language === "ar" ? "التقارير" : "Reports"
      case "/users":
        return language === "ar" ? "المستخدمون" : "Users"
      case "/settings":
        return language === "ar" ? "الإعدادات" : "Settings"
      default:
        return language === "ar" ? "لوحة التحكم" : "Dashboard"
    }
  }
  
  return (
    <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-2 px-4">
        <SidebarTrigger className={isRTL ? "-mr-1" : "-ml-1"} />
        <Separator orientation="vertical" className={`h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/">{language === "ar" ? "لوحة التحكم" : "Dashboard"}</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>{getCurrentPageName()}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <div className={`flex items-center gap-2 px-4 ${isRTL ? 'mr-auto' : 'ml-auto'}`}>
        <Button variant="outline" size="sm" onClick={() => setLanguage(language === "ar" ? "en" : "ar")}>
          <Languages className={`h-4 w-4 ${getIconMargin(isRTL)}`} />
          {language === "ar" ? "English" : "العربية"}
        </Button>
        <ModeToggle />
      </div>
    </header>
  )
}