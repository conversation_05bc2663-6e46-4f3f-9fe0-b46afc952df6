"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Upload, X } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"
import { createClient } from "@/lib/supabase-browser"
import { uploadMultipleLicenseImages } from "@/lib/vehicle-license-service"

interface AddVehicleDialogProps {
  language: "ar" | "en"
  onVehicleAdded?: () => void
}

export function AddVehicleDialog({ language, onVehicleAdded }: AddVehicleDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()
  const supabaseClient = createClient()
  
  // File input refs
  const frontImageRef = useRef<HTMLInputElement>(null)
  const backImageRef = useRef<HTMLInputElement>(null)
  
  // License image states
  const [licenseImages, setLicenseImages] = useState<{
    front?: File;
    back?: File;
  }>({})
  
  const [licenseImagePreviews, setLicenseImagePreviews] = useState<{
    front?: string;
    back?: string;
  }>({})

  const [formData, setFormData] = useState({
    plate_number: "",
    color: "",
    vehicle_type: "",
    service_type: "",
    fuel_type: "",
    year: "",
    vin: "",
    current_km: "",
    last_maintenance_km: "",
    last_tire_change_km: "",
    license_expiry: "",
    branch_id: "",
    status: "active",
    license_type: "",
    vehicle_features: "",
    permits: "",
  })
  const [branches, setBranches] = useState<Array<{ id: string; name: string }>>([])

  useEffect(() => {
    fetchBranches()
  }, [])

  // Handle license image selection
  const handleLicenseImageChange = (side: 'front' | 'back', file: File | null) => {
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: language === "ar" ? "نوع ملف غير مدعوم" : "Unsupported file type",
          description: language === "ar" ? "يرجى استخدام JPEG, PNG أو WebP" : "Please use JPEG, PNG or WebP",
          variant: "destructive"
        })
        return
      }
      
      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: language === "ar" ? "حجم الملف كبير" : "File too large",
          description: language === "ar" ? "الحد الأقصى 5 ميجابايت" : "Maximum size is 5MB",
          variant: "destructive"
        })
        return
      }
      
      // Update license images state
      setLicenseImages(prev => ({ ...prev, [side]: file }))
      
      // Create preview URL
      const previewUrl = URL.createObjectURL(file)
      setLicenseImagePreviews(prev => {
        // Clean up old preview URL
        if (prev[side]) {
          URL.revokeObjectURL(prev[side]!)
        }
        return { ...prev, [side]: previewUrl }
      })
    } else {
      // Remove image
      setLicenseImages(prev => {
        const newState = { ...prev }
        delete newState[side]
        return newState
      })
      
      // Clean up preview URL
      setLicenseImagePreviews(prev => {
        if (prev[side]) {
          URL.revokeObjectURL(prev[side]!)
        }
        const newState = { ...prev }
        delete newState[side]
        return newState
      })
    }
  }
  
  // Remove license image
  const removeLicenseImage = (side: 'front' | 'back') => {
    handleLicenseImageChange(side, null)
    
    // Reset file input
    if (side === 'front' && frontImageRef.current) {
      frontImageRef.current.value = ''
    } else if (side === 'back' && backImageRef.current) {
      backImageRef.current.value = ''
    }
  }

  const fetchBranches = async () => {
    try {
      console.log('Fetching branches...')
      const { data, error } = await supabaseClient
        .from('branches')
        .select('id, name')
        .order('name')
      
      console.log('Branches response:', { data, error })
      
      if (error) {
        console.error('Error fetching branches:', error)
        // For development/testing, let's try without RLS
        if (error.code === '42501') {
          console.warn('RLS policy blocking branches query. This might be due to missing tenant_id in JWT.')
        }
      } else {
        setBranches(data || [])
      }
    } catch (error) {
      console.error('Error fetching branches:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Validate required fields
      if (!formData.plate_number.trim()) {
        throw new Error(language === "ar" ? "رقم اللوحة مطلوب" : "Plate number is required")
      }
      
      // Sanitize plate number for database operations
      const sanitizedPlateNumber = formData.plate_number.trim().replace(/\s+/g, ' ')
      if (!formData.last_maintenance_km.trim()) {
        throw new Error(language === "ar" ? "عدد الكيلومترات عند آخر صيانة مطلوب" : "Last maintenance KM is required")
      }
      if (!formData.last_tire_change_km.trim()) {
        throw new Error(language === "ar" ? "عدد الكيلومترات عند آخر تغيير الإطارات مطلوب" : "Last tire change KM is required")
      }
      if (!formData.license_expiry) {
        throw new Error(language === "ar" ? "تاريخ انتهاء الرخصة مطلوب" : "License expiry date is required")
      }
      if (!formData.branch_id) {
        throw new Error(language === "ar" ? "الفرع مطلوب" : "Branch is required")
      }

      // Check for duplicate plate number
      const { data: existingVehicle, error: checkError } = await supabaseClient
        .from("vehicles")
        .select("plate_number")
        .eq("plate_number", sanitizedPlateNumber)
        .maybeSingle() // Use maybeSingle instead of single for better error handling

      if (checkError) {
        console.error('Error checking for duplicate plate number:', checkError)
        throw new Error(language === "ar" ? "خطأ في التحقق من رقم اللوحة" : "Error checking plate number")
      }

      if (existingVehicle) {
        throw new Error(
          language === "ar" 
            ? `المركبة برقم اللوحة ${formData.plate_number} موجودة بالفعل` 
            : `Vehicle with plate number ${formData.plate_number} already exists`
        )
      }

      // Prepare data for insertion - ensure all required fields are present
      const vehicleData = {
        plate_number: sanitizedPlateNumber,
        color: formData.color?.trim() || null,
        vehicle_type: formData.vehicle_type || null,
        service_type: formData.service_type || null,
        fuel_type: formData.fuel_type || null,
        year: formData.year ? Number.parseInt(formData.year) : null,
        vin: formData.vin?.trim() || null,
        current_km: formData.current_km ? Number.parseInt(formData.current_km) : null,
        last_maintenance_km: formData.last_maintenance_km ? Number.parseInt(formData.last_maintenance_km) : null,
        last_tire_change_km: formData.last_tire_change_km ? Number.parseInt(formData.last_tire_change_km) : null,
        license_expiry: formData.license_expiry || null,
        branch_id: formData.branch_id || null,
        status: formData.status || "active",
        license_type: formData.license_type || null,
        vehicle_features: formData.vehicle_features || null,
        permits: formData.permits || null
        // Note: created_at will be set automatically by database DEFAULT NOW()
      }

      // Comprehensive debugging as required by project specifications
      console.log('=== VEHICLE INSERT DEBUG START ===', {
        timestamp: new Date().toISOString(),
        formData: formData,
        vehicleData: vehicleData,
        fieldMapping: {
          frontend_license_field: 'license_expiry',
          database_license_field: 'license_expiry',
          value: formData.license_expiry
        }
      })

      // Try to insert into Supabase with comprehensive error handling
      const { data, error } = await supabaseClient.from("vehicles").insert([vehicleData]).select()

      // Detailed response logging for debugging silent failures
      console.log('=== SUPABASE INSERT RESPONSE ===', {
        timestamp: new Date().toISOString(),
        success: !error && data && data.length > 0,
        data: data,
        error: error,
        errorDetails: {
          message: error?.message,
          details: error?.details,
          hint: error?.hint,
          code: error?.code,
          isEmpty: error && Object.keys(error).length === 0
        },
        requestPayload: vehicleData
      })

      if (error) {
        // Enhanced error logging to catch silent failures
        const errorInfo = {
          error: error,
          message: error.message || 'Unknown error',
          details: error.details || 'No details',
          hint: error.hint || 'No hint',
          code: error.code || 'No code',
          isEmptyError: Object.keys(error).length === 0,
          fullErrorObject: JSON.stringify(error),
          requestData: vehicleData
        }
        
        console.error('=== SUPABASE INSERT FAILED ===', errorInfo)
        throw new Error(
          error.message || 
          `Failed to insert vehicle. Error details: ${JSON.stringify(errorInfo)}`
        )
      }

      // Verify data was actually inserted
      if (!data || data.length === 0) {
        throw new Error(language === "ar" ? "فشل إدراج البيانات" : "Failed to insert data")
      }

      const newVehicleId = data[0].id
      console.log('✅ Vehicle inserted successfully with ID:', newVehicleId)

      // Upload license images if provided
      if (licenseImages.front || licenseImages.back) {
        console.log('📸 Starting license image uploads...')
        
        const uploadResult = await uploadMultipleLicenseImages(newVehicleId, licenseImages)
        
        if (!uploadResult.success) {
          console.warn('⚠️ Some license images failed to upload:', uploadResult.error)
          
          toast({
            title: language === "ar" ? "تم إضافة المركبة مع تحذير" : "Vehicle Added with Warning",
            description: 
              language === "ar"
                ? `تم إضافة المركبة ${formData.plate_number} لكن فشل رفع بعض صور الرخصة`
                : `Vehicle ${formData.plate_number} added but some license images failed to upload`,
            variant: "default"
          })
        } else {
          console.log('✅ All license images uploaded successfully')
          
          toast({
            title: language === "ar" ? "تم إضافة المركبة" : "Vehicle Added",
            description:
              language === "ar"
                ? `تم إضافة المركبة ${formData.plate_number} مع صور الرخصة بنجاح`
                : `Vehicle ${formData.plate_number} has been added with license images successfully`,
          })
        }
      } else {
        // No images to upload
        toast({
          title: language === "ar" ? "تم إضافة المركبة" : "Vehicle Added",
          description:
            language === "ar"
              ? `تم إضافة المركبة ${formData.plate_number} بنجاح`
              : `Vehicle ${formData.plate_number} has been added successfully`,
        })
      }

      // Reset form and close dialog
      setFormData({
        plate_number: "",
        color: "",
        vehicle_type: "",
        service_type: "",
        fuel_type: "",
        year: "",
        vin: "",
        current_km: "",
        last_maintenance_km: "",
        last_tire_change_km: "",
        license_expiry: "",
        branch_id: "",
        status: "active",
        license_type: "",
        vehicle_features: "",
        permits: "",
      })
      
      // Reset license images
      Object.values(licenseImagePreviews).forEach(url => {
        if (url) URL.revokeObjectURL(url)
      })
      setLicenseImages({})
      setLicenseImagePreviews({})
      
      // Reset file inputs
      if (frontImageRef.current) frontImageRef.current.value = ''
      if (backImageRef.current) backImageRef.current.value = ''
      
      setOpen(false)

      // Trigger refresh if callback provided
      if (onVehicleAdded) {
        onVehicleAdded()
      }
    } catch (err: any) {
      // Comprehensive error debugging as required by project specifications
      const errorInfo = {
        timestamp: new Date().toISOString(),
        error: err,
        message: err?.message || 'Unknown error',
        details: err?.details || 'No details',
        hint: err?.hint || 'No hint',
        code: err?.code || 'No code',
        stack: err?.stack || 'No stack',
        isEmptyError: err && Object.keys(err).length === 0,
        fullErrorObject: JSON.stringify(err),
        formData: formData,
        vehicleData: {
          plate_number: formData.plate_number.trim(),
          license_expiry: formData.license_expiry,
          branch_id: formData.branch_id
        }
      }
      
      console.error('=== VEHICLE INSERT ERROR ===', errorInfo)
      
      // Additional debug output for field mapping verification
      console.log('=== FIELD MAPPING VERIFICATION ===', {
        frontend_field: 'license_expiry',
        database_field: 'license_expiry', 
        value: formData.license_expiry,
        isMatching: true,
        previousIssue: 'license_expiration vs license_expiry mismatch - FIXED'
      })
      
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description: err.message || (language === "ar" ? "حدث خطأ أثناء إضافة المركبة" : "An error occurred while adding the vehicle"),
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          {language === "ar" ? "إضافة مركبة" : "Add Vehicle"}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>{language === "ar" ? "إضافة مركبة جديدة" : "Add New Vehicle"}</DialogTitle>
          <DialogDescription>
            {language === "ar" ? "أدخل تفاصيل المركبة الجديدة" : "Enter the details of the new vehicle"}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto">
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="plate_number">{language === "ar" ? "رقم اللوحة" : "Plate Number"} *</Label>
                <Input
                  id="plate_number"
                  value={formData.plate_number}
                  onChange={(e) => setFormData({ ...formData, plate_number: e.target.value })}
                  placeholder={language === "ar" ? "ABC-123" : "ABC-123"}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="color">{language === "ar" ? "اللون" : "Color"}</Label>
                <Select
                  value={formData.color}
                  onValueChange={(value) => setFormData({ ...formData, color: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر اللون" : "Select color"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Black">{language === "ar" ? "أسود" : "Black"}</SelectItem>
                    <SelectItem value="Grey">{language === "ar" ? "رمادي" : "Grey"}</SelectItem>
                    <SelectItem value="Silver">{language === "ar" ? "فضي" : "Silver"}</SelectItem>
                    <SelectItem value="Blue">{language === "ar" ? "أزرق" : "Blue"}</SelectItem>
                    <SelectItem value="Red">{language === "ar" ? "أحمر" : "Red"}</SelectItem>
                    <SelectItem value="White">{language === "ar" ? "أبيض" : "White"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="vehicle_type">{language === "ar" ? "نوع المركبة" : "Vehicle Type"}</Label>
                <Select
                  value={formData.vehicle_type}
                  onValueChange={(value) => setFormData({ ...formData, vehicle_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر النوع" : "Select type"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="E-Cab">{language === "ar" ? "كاب" : "E-Cab"}</SelectItem>
                  <SelectItem value="Comfort">{language === "ar" ? "كومفورت" : "Comfort"}</SelectItem>
                  <SelectItem value="Comfort+">{language === "ar" ? "كومفورت بلص" : "Comfort+"}</SelectItem>
                  <SelectItem value="Compact">{language === "ar" ? "كومبكت" : "Compact"}</SelectItem>
                  <SelectItem value="Premium">{language === "ar" ? "برايميوم" : "Premium"}</SelectItem>
                  <SelectItem value="E-Class">{language === "ar" ? "مرسيدس" : "E-Class"}</SelectItem>
                  <SelectItem value="Van">{language === "ar" ? "فان" : "Van"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="service_type">{language === "ar" ? "نوع الخدمة" : "Service Type"}</Label>
                <Select
                  value={formData.service_type}
                  onValueChange={(value) => setFormData({ ...formData, service_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر الخدمة" : "Select service"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="LEVC">{language === "ar" ? "ليفك" : "LEVC"}</SelectItem>
                  <SelectItem value="Comfort">{language === "ar" ? "كومفورت" : "Comfort"}</SelectItem>
                  <SelectItem value="Comfort+">{language === "ar" ? "كومفورت بلص" : "Comfort+"}</SelectItem>
                  <SelectItem value="Compact">{language === "ar" ? "كومبكت" : "Compact"}</SelectItem>
                  <SelectItem value="Premium">{language === "ar" ? "برايميوم" : "Premium"}</SelectItem>
                  <SelectItem value="E-Class">{language === "ar" ? "مرسيدس" : "E-Class"}</SelectItem>
                  <SelectItem value="Van">{language === "ar" ? "فان" : "Van"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="fuel_type">{language === "ar" ? "نوع الوقود" : "Fuel Type"}</Label>
                <Select
                  value={formData.fuel_type}
                  onValueChange={(value) => setFormData({ ...formData, fuel_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر الوقود" : "Select fuel"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gasoline_95">{language === "ar" ? "بنزين 95" : "Gasoline 95"}</SelectItem>
                    <SelectItem value="gasoline_92">{language === "ar" ? "بنزين 92" : "Gasoline 92"}</SelectItem>
                    <SelectItem value="diesel">{language === "ar" ? "ديزل" : "Diesel"}</SelectItem>
                    <SelectItem value="electric">{language === "ar" ? "كهربائي" : "Electric"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="year">{language === "ar" ? "سنة الصنع" : "Year"}</Label>
                <Input
                  id="year"
                  type="number"
                  value={formData.year}
                  onChange={(e) => setFormData({ ...formData, year: e.target.value })}
                  placeholder="2023"
                  min="1990"
                  max="2030"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="license_type">{language === "ar" ? "نوع الترخيص" : "License Type"}</Label>
                <Select
                  value={formData.license_type}
                  onValueChange={(value) => setFormData({ ...formData, license_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر نوع الترخيص" : "Select license type"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Private">{language === "ar" ? "خاص" : "Private"}</SelectItem>
                    <SelectItem value="Tourism">{language === "ar" ? "سياحي" : "Tourism"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="vehicle_features">{language === "ar" ? "تفاصيل المركبة" : "Vehicle Features"}</Label>
                <Select
                  value={formData.vehicle_features}
                  onValueChange={(value) => setFormData({ ...formData, vehicle_features: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر التفاصيل" : "Select features"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Ramp">{language === "ar" ? "رامب" : "Ramp"}</SelectItem>
                    <SelectItem value="No Ramp">{language === "ar" ? "بدون رامب" : "No Ramp"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="permits">{language === "ar" ? "التصاريح" : "Permits"}</Label>
                <Select
                  value={formData.permits}
                  onValueChange={(value) => setFormData({ ...formData, permits: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر التصاريح" : "Select permits"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Airport Permit">{language === "ar" ? "تصريح مطار" : "Airport Permit"}</SelectItem>
                    <SelectItem value="None">{language === "ar" ? "لا يوجد" : "None"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="vin">{language === "ar" ? "رقم الهيكل" : "VIN"}</Label>
                <Input
                  id="vin"
                  value={formData.vin}
                  onChange={(e) => setFormData({ ...formData, vin: e.target.value })}
                  placeholder="1HGBH41JXMN109186"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="current_km">{language === "ar" ? "الكيلومترات الحالية" : "Current KM"}</Label>
                <Input
                  id="current_km"
                  type="number"
                  value={formData.current_km}
                  onChange={(e) => setFormData({ ...formData, current_km: e.target.value })}
                  placeholder="50000"
                  min="0"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="last_maintenance_km">{language === "ar" ? "آخر صيانة (كم)" : "Last Maintenance (KM)"} *</Label>
                <Input
                  id="last_maintenance_km"
                  type="number"
                  value={formData.last_maintenance_km}
                  onChange={(e) => setFormData({ ...formData, last_maintenance_km: e.target.value })}
                  placeholder="45000"
                  min="0"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="last_tire_change_km">{language === "ar" ? "آخر تغيير إطارات (كم)" : "Last Tire Change (KM)"} *</Label>
                <Input
                  id="last_tire_change_km"
                  type="number"
                  value={formData.last_tire_change_km}
                  onChange={(e) => setFormData({ ...formData, last_tire_change_km: e.target.value })}
                  placeholder="30000"
                  min="0"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="license_expiry">{language === "ar" ? "تاريخ انتهاء الرخصة" : "License Expiry Date"} *</Label>
                <Input
                  id="license_expiry"
                  type="date"
                  value={formData.license_expiry}
                  onChange={(e) => setFormData({ ...formData, license_expiry: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="branch_id">{language === "ar" ? "الفرع" : "Branch"} *</Label>
                <Select
                  value={formData.branch_id}
                  onValueChange={(value) => setFormData({ ...formData, branch_id: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر الفرع" : "Select branch"} />
                  </SelectTrigger>
                  <SelectContent>
                    {branches.map((branch) => (
                      <SelectItem key={branch.id} value={branch.id}>
                        {branch.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <Label htmlFor="status">{language === "ar" ? "الحالة" : "Status"}</Label>
              <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">{language === "ar" ? "نشط" : "Active"}</SelectItem>
                  <SelectItem value="maintenance">{language === "ar" ? "صيانة" : "Maintenance"}</SelectItem>
                  <SelectItem value="out_of_service">{language === "ar" ? "خارج الخدمة" : "Out of Service"}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* License Images Section */}
            <div className="space-y-4 border-t pt-4">
              <div className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                <Label className="text-base font-medium">
                  {language === "ar" ? "صور الرخصة" : "License Images"}
                </Label>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Front License Image */}
                <div className="space-y-2">
                  <Label htmlFor="front_license">
                    {language === "ar" ? "الرخصة الأمامية" : "Front License"}
                  </Label>
                  
                  {licenseImagePreviews.front ? (
                    <div className="relative">
                      <img
                        src={licenseImagePreviews.front}
                        alt={language === "ar" ? "الرخصة الأمامية" : "Front License"}
                        className="w-full h-32 object-cover rounded-lg border"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={() => removeLicenseImage('front')}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                      <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-600 mb-2">
                        {language === "ar" ? "اضغط لرفع صورة الرخصة الأمامية" : "Click to upload front license image"}
                      </p>
                      <Input
                        ref={frontImageRef}
                        type="file"
                        accept="image/jpeg,image/jpg,image/png,image/webp"
                        onChange={(e) => {
                          const file = e.target.files?.[0]
                          if (file) handleLicenseImageChange('front', file)
                        }}
                        className="hidden"
                        id="front_license"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => frontImageRef.current?.click()}
                      >
                        {language === "ar" ? "اختيار ملف" : "Choose File"}
                      </Button>
                    </div>
                  )}
                </div>

                {/* Back License Image */}
                <div className="space-y-2">
                  <Label htmlFor="back_license">
                    {language === "ar" ? "الرخصة الخلفية" : "Back License"}
                  </Label>
                  
                  {licenseImagePreviews.back ? (
                    <div className="relative">
                      <img
                        src={licenseImagePreviews.back}
                        alt={language === "ar" ? "الرخصة الخلفية" : "Back License"}
                        className="w-full h-32 object-cover rounded-lg border"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={() => removeLicenseImage('back')}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                      <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-600 mb-2">
                        {language === "ar" ? "اضغط لرفع صورة الرخصة الخلفية" : "Click to upload back license image"}
                      </p>
                      <Input
                        ref={backImageRef}
                        type="file"
                        accept="image/jpeg,image/jpg,image/png,image/webp"
                        onChange={(e) => {
                          const file = e.target.files?.[0]
                          if (file) handleLicenseImageChange('back', file)
                        }}
                        className="hidden"
                        id="back_license"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => backImageRef.current?.click()}
                      >
                        {language === "ar" ? "اختيار ملف" : "Choose File"}
                      </Button>
                    </div>
                  )}
                </div>
              </div>
              
              <p className="text-sm text-gray-500">
                {language === "ar" 
                  ? "يمكنك رفع صور بصيغة JPEG, PNG أو WebP. الحد الأقصى 5 ميجابايت."
                  : "You can upload images in JPEG, PNG or WebP format. Maximum size is 5MB."
                }
              </p>
            </div>
          </div>
          <DialogFooter className="mt-4">
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              {language === "ar" ? "إلغاء" : "Cancel"}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (language === "ar" ? "جاري الإضافة..." : "Adding...") : language === "ar" ? "إضافة" : "Add"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
