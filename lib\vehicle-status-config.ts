// Vehicle status configuration with consistent colors across the application
export const vehicleStatusConfig = {
  active: { 
    variant: "default" as const, 
    label: { ar: "نشط", en: "Active" },
    color: "bg-green-500"
  },
  maintenance: { 
    variant: "secondary" as const, 
    label: { ar: "صيانة", en: "Maintenance" },
    color: "bg-orange-500"
  },
  out_of_service: { 
    variant: "destructive" as const, 
    label: { ar: "خارج الخدمة", en: "Out of Service" },
    color: "bg-red-500"
  },
  retired: { 
    variant: "outline" as const, 
    label: { ar: "متقاعد", en: "Retired" },
    color: "bg-gray-500"
  },
  inspection: { 
    variant: "secondary" as const, 
    label: { ar: "فحص", en: "Inspection" },
    color: "bg-blue-500"
  }
} as const;

export type VehicleStatus = keyof typeof vehicleStatusConfig;

export const getVehicleStatusConfig = (status: string, language: "ar" | "en" = "en") => {
  const config = vehicleStatusConfig[status as VehicleStatus] || vehicleStatusConfig.active;
  return {
    ...config,
    label: config.label[language]
  };
};