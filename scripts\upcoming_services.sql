CREATE OR REPLACE VIEW public.upcoming_services AS
-- 1. الصيانة الدورية القادمة بناءً على الكيلومترات
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'maintenance_km' AS service_type,
    'صيانة دورية' AS service_type_ar,
    v.next_maintenance_km AS trigger_value,
    v.current_km AS current_value,
    (v.next_maintenance_km - v.current_km) AS remaining_km,
    NULL::date AS due_date,
    NULL::integer AS reminder_days,
    v.branch_id
FROM 
    vehicles v
WHERE 
    v.next_maintenance_km IS NOT NULL 
    AND v.current_km IS NOT NULL
    AND (v.next_maintenance_km - v.current_km) < 1000 -- إظهار عندما تتبقى 1000 كم أو أقل

UNION ALL

-- 2. الصيانة الدورية القادمة بناءً على التاريخ
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'maintenance_date' AS service_type,
    'صيانة دورية' AS service_type_ar,
    NULL::integer AS trigger_value,
    NULL::integer AS current_value,
    NULL::integer AS remaining_km,
    v.next_maintenance_date AS due_date,
    30 AS reminder_days, -- تنبيه قبل 30 يوم
    v.branch_id
FROM 
    vehicles v
WHERE 
    v.next_maintenance_date IS NOT NULL 
    AND v.next_maintenance_date <= (CURRENT_DATE + INTERVAL '30 days')

UNION ALL

-- 3. تغيير الإطارات القادم بناءً على الكيلومترات
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'tire_change_km' AS service_type,
    'تغيير إطارات' AS service_type_ar,
    v.next_tire_change_km AS trigger_value,
    v.current_km AS current_value,
    (v.next_tire_change_km - v.current_km) AS remaining_km,
    NULL::date AS due_date,
    NULL::integer AS reminder_days,
    v.branch_id
FROM 
    vehicles v
WHERE 
    v.next_tire_change_km IS NOT NULL 
    AND v.current_km IS NOT NULL
    AND (v.next_tire_change_km - v.current_km) < 1000 -- إظهار عندما تتبقى 1000 كم أو أقل

UNION ALL

-- 4. تغيير الإطارات القادم بناءً على التاريخ
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'tire_change_date' AS service_type,
    'تغيير إطارات' AS service_type_ar,
    NULL::integer AS trigger_value,
    NULL::integer AS current_value,
    NULL::integer AS remaining_km,
    v.next_tire_change_date AS due_date,
    30 AS reminder_days, -- تنبيه قبل 30 يوم
    v.branch_id
FROM 
    vehicles v
WHERE 
    v.next_tire_change_date IS NOT NULL 
    AND v.next_tire_change_date <= (CURRENT_DATE + INTERVAL '30 days')

UNION ALL

-- 5. انتهاء رخصة المركبة
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'license_expiry' AS service_type,
    'انتهاء رخصة المركبة' AS service_type_ar,
    NULL::integer AS trigger_value,
    NULL::integer AS current_value,
    NULL::integer AS remaining_km,
    v.license_expiry AS due_date,
    30 AS reminder_days, -- تنبيه قبل 30 يوم
    v.branch_id
FROM 
    vehicles v
WHERE 
    v.license_expiry IS NOT NULL 
    AND v.license_expiry <= (CURRENT_DATE + INTERVAL '30 days')

UNION ALL

-- 6. انتهاء التأمين
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'insurance_expiry' AS service_type,
    'انتهاء التأمين' AS service_type_ar,
    NULL::integer AS trigger_value,
    NULL::integer AS current_value,
    NULL::integer AS remaining_km,
    v.insurance_expiry AS due_date,
    30 AS reminder_days, -- تنبيه قبل 30 يوم
    v.branch_id
FROM 
    vehicles v
WHERE 
    v.insurance_expiry IS NOT NULL 
    AND v.insurance_expiry <= (CURRENT_DATE + INTERVAL '30 days')

UNION ALL

-- 7. انتهاء رخصة السائق (من جدول drivers)
SELECT 
    v.id AS vehicle_id,
    v.plate_number,
    'driver_license_expiry' AS service_type,
    'انتهاء رخصة السائق' AS service_type_ar,
    NULL::integer AS trigger_value,
    NULL::integer AS current_value,
    NULL::integer AS remaining_km,
    d.license_expiry AS due_date,
    30 AS reminder_days, -- تنبيه قبل 30 يوم
    v.branch_id
FROM 
    vehicles v
    JOIN vehicle_assignments va ON v.id = va.vehicle_id AND va.end_date IS NULL
    JOIN drivers d ON va.driver_id = d.id
WHERE 
    d.license_expiry IS NOT NULL 
    AND d.license_expiry <= (CURRENT_DATE + INTERVAL '30 days')

ORDER BY 
    due_date ASC NULLS LAST, 
    remaining_km ASC NULLS LAST;