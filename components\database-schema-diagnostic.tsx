"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { createClient } from "@/lib/supabase-browser"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface ColumnInfo {
  column_name: string
  data_type: string
  is_nullable: string
  column_default: string | null
}

interface SchemaResult {
  tableName: string
  columns: ColumnInfo[]
  status: 'success' | 'error'
  error?: any
}

export function DatabaseSchemaDiagnostic() {
  const [results, setResults] = useState<SchemaResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const introspectDatabase = async () => {
    setIsRunning(true)
    setResults([])
    const supabaseClient = createClient()

    const tablesToCheck = ['vehicles', 'users', 'branches', 'drivers']

    try {
      for (const tableName of tablesToCheck) {
        try {
          // Query PostgreSQL information_schema to get actual column definitions
          const { data: columns, error } = await supabaseClient
            .from('information_schema.columns')
            .select('column_name, data_type, is_nullable, column_default')
            .eq('table_name', tableName)
            .eq('table_schema', 'public')
            .order('ordinal_position')

          if (error) {
            setResults(prev => [...prev, {
              tableName,
              columns: [],
              status: 'error',
              error: error
            }])
          } else {
            setResults(prev => [...prev, {
              tableName,
              columns: columns || [],
              status: 'success'
            }])
          }
        } catch (error) {
          setResults(prev => [...prev, {
            tableName,
            columns: [],
            status: 'error',
            error: error
          }])
        }
      }
    } catch (error) {
      console.error('Database introspection failed:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const generateMigrationSQL = () => {
    // Expected columns for vehicles table based on schema
    const expectedVehicleColumns = [
      'id', 'plate_number', 'color', 'vehicle_type', 'service_type', 
      'fuel_type', 'year', 'vin', 'current_km', 'last_maintenance_km', 
      'next_maintenance_km', 'next_maintenance_date', 'last_tire_change_km', 
      'next_tire_change_km', 'next_tire_change_date', 'insurance_expiry', 
      'license_expiry', 'status', 'branch_id', 'created_at'
    ]

    const vehicleResult = results.find(r => r.tableName === 'vehicles')
    if (!vehicleResult || vehicleResult.status === 'error') {
      return 'Could not analyze vehicles table structure'
    }

    const existingColumns = vehicleResult.columns.map(c => c.column_name)
    const missingColumns = expectedVehicleColumns.filter(col => !existingColumns.includes(col))

    if (missingColumns.length === 0) {
      return 'No missing columns detected'
    }

    const migrations = missingColumns.map(col => {
      switch (col) {
        case 'vehicle_type':
          return 'ALTER TABLE vehicles ADD COLUMN vehicle_type VARCHAR(100);'
        case 'service_type':
          return 'ALTER TABLE vehicles ADD COLUMN service_type VARCHAR(100);'
        case 'color':
          return 'ALTER TABLE vehicles ADD COLUMN color VARCHAR(50);'
        case 'year':
          return 'ALTER TABLE vehicles ADD COLUMN year INTEGER;'
        case 'vin':
          return 'ALTER TABLE vehicles ADD COLUMN vin VARCHAR(17);'
        case 'current_km':
          return 'ALTER TABLE vehicles ADD COLUMN current_km INTEGER DEFAULT 0;'
        case 'last_maintenance_km':
          return 'ALTER TABLE vehicles ADD COLUMN last_maintenance_km INTEGER;'
        case 'next_maintenance_km':
          return 'ALTER TABLE vehicles ADD COLUMN next_maintenance_km INTEGER;'
        case 'next_maintenance_date':
          return 'ALTER TABLE vehicles ADD COLUMN next_maintenance_date DATE;'
        case 'last_tire_change_km':
          return 'ALTER TABLE vehicles ADD COLUMN last_tire_change_km INTEGER;'
        case 'next_tire_change_km':
          return 'ALTER TABLE vehicles ADD COLUMN next_tire_change_km INTEGER;'
        case 'next_tire_change_date':
          return 'ALTER TABLE vehicles ADD COLUMN next_tire_change_date DATE;'
        case 'insurance_expiry':
          return 'ALTER TABLE vehicles ADD COLUMN insurance_expiry DATE;'
        case 'license_expiry':
          return 'ALTER TABLE vehicles ADD COLUMN license_expiry DATE;'
        case 'fuel_type':
          return 'ALTER TABLE vehicles ADD COLUMN fuel_type fuel_type_enum;'
        case 'status':
          return 'ALTER TABLE vehicles ADD COLUMN status vehicle_status_enum DEFAULT \'active\';'
        case 'branch_id':
          return 'ALTER TABLE vehicles ADD COLUMN branch_id UUID REFERENCES branches(id) ON DELETE SET NULL;'
        default:
          return `-- Unknown column: ${col}`
      }
    })

    return `-- Migration SQL to add missing columns to vehicles table:\n\n${migrations.join('\n')}`
  }

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <CardTitle>Database Schema Diagnostic</CardTitle>
        <CardDescription>
          Introspect database to check actual table structure vs expected schema
        </CardDescription>
        <div className="flex gap-2">
          <Button onClick={introspectDatabase} disabled={isRunning}>
            {isRunning ? 'Checking Database...' : 'Check Database Schema'}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-6">
          {results.map((result, index) => (
            <div key={index} className="border rounded-lg p-4">
              <div className="flex items-center gap-2 mb-4">
                <Badge className={result.status === 'success' ? 'bg-green-500' : 'bg-red-500'}>
                  {result.status.toUpperCase()}
                </Badge>
                <span className="font-medium text-lg">{result.tableName} table</span>
              </div>
              
              {result.status === 'error' ? (
                <Alert>
                  <AlertDescription>
                    Error checking table: {JSON.stringify(result.error)}
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-2">
                  <h4 className="font-medium">Columns ({result.columns.length}):</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {result.columns.map((col, colIndex) => (
                      <div key={colIndex} className="text-sm p-2 bg-gray-50 rounded">
                        <div className="font-mono font-medium">{col.column_name}</div>
                        <div className="text-gray-600">{col.data_type}</div>
                        <div className="text-xs text-gray-500">
                          {col.is_nullable === 'YES' ? 'Nullable' : 'Not Null'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
          
          {results.length > 0 && (
            <div className="border rounded-lg p-4">
              <h3 className="font-medium mb-4">Migration SQL</h3>
              <pre className="text-xs p-4 bg-gray-100 rounded overflow-auto whitespace-pre-wrap">
                {generateMigrationSQL()}
              </pre>
            </div>
          )}
          
          {results.length === 0 && !isRunning && (
            <Alert>
              <AlertDescription>
                Click "Check Database Schema" to introspect your actual database structure.
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  )
}