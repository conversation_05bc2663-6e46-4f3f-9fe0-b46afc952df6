"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { useUpcomingServices } from "@/hooks/use-upcoming-services"
import { LoadingSpinner } from "@/components/loading-spinner"
import { ErrorDisplay } from "@/components/error-display"
import { Clock, Calendar as CalendarIcon, Wrench, Car, Download, Filter } from "lucide-react"
import { useState, useMemo } from "react"
import { format } from "date-fns"
import { ar, enUS } from "date-fns/locale"

interface UpcomingServicesProps {
  language: "ar" | "en"
  limit?: number
}

export function UpcomingServices({ language, limit }: UpcomingServicesProps) {
  // Filter states
  const [plateNumberFilter, setPlateNumberFilter] = useState("")
  const [serviceTypeFilter, setServiceTypeFilter] = useState("")
  const [priorityFilter, setPriorityFilter] = useState("")
  const [dateFrom, setDateFrom] = useState<Date | undefined>()
  const [dateTo, setDateTo] = useState<Date | undefined>()
  const [showFilters, setShowFilters] = useState(false)

  // Apply filters
  const filter = useMemo(() => {
    return {
      plateNumber: plateNumberFilter,
      serviceType: serviceTypeFilter,
      priority: priorityFilter as "urgent" | "important" | "normal" || undefined,
      dateRange: {
        from: dateFrom ? format(dateFrom, "yyyy-MM-dd") : undefined,
        to: dateTo ? format(dateTo, "yyyy-MM-dd") : undefined
      }
    }
  }, [plateNumberFilter, serviceTypeFilter, priorityFilter, dateFrom, dateTo])

  const { data: services, loading, error } = useUpcomingServices(limit, filter)

  const exportToExcel = () => {
    // Simple CSV export (Excel compatible) that respects current filters
    const headers = [
      language === "ar" ? "رقم اللوحة" : "Plate Number",
      language === "ar" ? "نوع الخدمة" : "Service Type",
      language === "ar" ? "ال_TRIGGER" : "Trigger",
      language === "ar" ? "المتبقي" : "Remaining",
      language === "ar" ? "الأولوية" : "Priority"
    ].join(',')
    
    const rows = services.map(service => {
      // Get service type label
      const serviceTypeLabel = language === "ar" ? (service.service_type_ar || service.service_type) : service.service_type
      
      // Get trigger value
      let triggerValue = "-"
      if (service.due_date) {
        triggerValue = new Date(service.due_date).toLocaleDateString(language === "ar" ? "ar-EG" : "en-EG")
      } else if (service.trigger_value) {
        triggerValue = `${service.trigger_value.toLocaleString()} km`
      }
      
      // Get remaining value
      let remainingValue = "-"
      if (service.due_date) {
        const daysUntilDue = service.due_date ? 
          Math.ceil((new Date(service.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 
          null
        if (daysUntilDue !== null) {
          remainingValue = `${daysUntilDue} ${language === "ar" ? "يوم" : "days"}`
        }
      } else if (service.remaining_km !== null) {
        remainingValue = `${service.remaining_km?.toLocaleString()} km`
      }
      
      // Get priority
      const daysUntilDue = service.due_date ? 
        Math.ceil((new Date(service.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 
        null
      const remainingKm = service.remaining_km
      
      let priority = language === "ar" ? "عادي" : "Normal"
      if ((daysUntilDue !== null && daysUntilDue <= 7) || (remainingKm !== null && remainingKm <= 500)) {
        priority = language === "ar" ? "عاجل" : "Urgent"
      } else if ((daysUntilDue !== null && daysUntilDue <= 30) || (remainingKm !== null && remainingKm <= 1000)) {
        priority = language === "ar" ? "مهم" : "Important"
      }
      
      return [
        `"${service.plate_number}"`,
        `"${serviceTypeLabel}"`,
        `"${triggerValue}"`,
        `"${remainingValue}"`,
        `"${priority}"`
      ].join(',')
    })

    const csvContent = [headers, ...rows].join('\n')
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute('download', `upcoming_services_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (loading) {
    return <LoadingSpinner className="h-64" />
  }

  if (error) {
    return <ErrorDisplay error={error} />
  }

  const getServiceTypeLabel = (serviceType: string, serviceTypeAr: string) => {
    if (language === "ar") {
      return serviceTypeAr || serviceType
    }
    return serviceType
  }

  const getServiceIcon = (serviceType: string) => {
    switch (serviceType) {
      case 'maintenance_km':
      case 'maintenance_date':
        return <Wrench className="h-4 w-4" />
      case 'tire_change_km':
      case 'tire_change_date':
        return <Car className="h-4 w-4" />
      case 'license_expiry':
      case 'insurance_expiry':
      case 'driver_license_expiry':
        return <CalendarIcon className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getDaysUntilDue = (dueDate: string | null) => {
    if (!dueDate) return null
    try {
      const due = new Date(dueDate)
      const today = new Date()
      const diffTime = due.getTime() - today.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays
    } catch {
      return null
    }
  }

  const getRemainingKm = (remainingKm: number | null) => {
    if (remainingKm === null) return null
    return remainingKm
  }

  const getPriorityBadge = (daysUntilDue: number | null, remainingKm: number | null) => {
    // High priority if due date is within 7 days or remaining km is less than 500
    if ((daysUntilDue !== null && daysUntilDue <= 7) || (remainingKm !== null && remainingKm <= 500)) {
      return <Badge variant="destructive">{language === "ar" ? "عاجل" : "Urgent"}</Badge>
    }
    
    // Medium priority if due date is within 30 days or remaining km is less than 1000
    if ((daysUntilDue !== null && daysUntilDue <= 30) || (remainingKm !== null && remainingKm <= 1000)) {
      return <Badge variant="secondary">{language === "ar" ? "مهم" : "Important"}</Badge>
    }
    
    // Low priority otherwise
    return <Badge variant="outline">{language === "ar" ? "عادي" : "Normal"}</Badge>
  }

  const clearFilters = () => {
    setPlateNumberFilter("")
    setServiceTypeFilter("")
    setPriorityFilter("")
    setDateFrom(undefined)
    setDateTo(undefined)
  }

  const hasActiveFilters = plateNumberFilter || serviceTypeFilter || priorityFilter || dateFrom || dateTo

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <CardTitle>{language === "ar" ? "الخدمات القادمة" : "Upcoming Services"}</CardTitle>
            <CardDescription>
              {language === "ar" ? "الصيانة والخدمات المجدولة القادمة" : "Scheduled maintenance and services"}
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              {language === "ar" ? "الفلاتر" : "Filters"}
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={exportToExcel}
              className="flex items-center gap-2"
              disabled={services.length === 0}
            >
              <Download className="h-4 w-4" />
              {language === "ar" ? "تصدير Excel" : "Export Excel"}
            </Button>
          </div>
        </div>
        
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mt-4 p-4 bg-muted rounded-lg">
            <div className="space-y-2">
              <Label htmlFor="plate-number">
                {language === "ar" ? "رقم اللوحة" : "Plate Number"}
              </Label>
              <Input
                id="plate-number"
                placeholder={language === "ar" ? "بحث برقم اللوحة" : "Search by plate number"}
                value={plateNumberFilter}
                onChange={(e) => setPlateNumberFilter(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="service-type">
                {language === "ar" ? "نوع الخدمة" : "Service Type"}
              </Label>
              <Select value={serviceTypeFilter} onValueChange={setServiceTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={language === "ar" ? "اختر نوع الخدمة" : "Select service type"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="maintenance_km">
                    {language === "ar" ? "صيانة دورية (كم)" : "Maintenance (km)"}
                  </SelectItem>
                  <SelectItem value="maintenance_date">
                    {language === "ar" ? "صيانة دورية (تاريخ)" : "Maintenance (date)"}
                  </SelectItem>
                  <SelectItem value="tire_change_km">
                    {language === "ar" ? "تغيير إطارات (كم)" : "Tire Change (km)"}
                  </SelectItem>
                  <SelectItem value="tire_change_date">
                    {language === "ar" ? "تغيير إطارات (تاريخ)" : "Tire Change (date)"}
                  </SelectItem>
                  <SelectItem value="license_expiry">
                    {language === "ar" ? "انتهاء رخصة المركبة" : "License Expiry"}
                  </SelectItem>
                  <SelectItem value="insurance_expiry">
                    {language === "ar" ? "انتهاء التأمين" : "Insurance Expiry"}
                  </SelectItem>
                  <SelectItem value="driver_license_expiry">
                    {language === "ar" ? "انتهاء رخصة السائق" : "Driver License Expiry"}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="priority">
                {language === "ar" ? "الأولوية" : "Priority"}
              </Label>
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger>
                  <SelectValue placeholder={language === "ar" ? "اختر الأولوية" : "Select priority"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="urgent">
                    {language === "ar" ? "عاجل" : "Urgent"}
                  </SelectItem>
                  <SelectItem value="important">
                    {language === "ar" ? "مهم" : "Important"}
                  </SelectItem>
                  <SelectItem value="normal">
                    {language === "ar" ? "عادي" : "Normal"}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="date-from">
                {language === "ar" ? "من تاريخ" : "From Date"}
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !dateFrom && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateFrom ? format(dateFrom, "PPP", { locale: language === "ar" ? ar : enUS }) : 
                     (language === "ar" ? "اختر تاريخ" : "Pick a date")}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={dateFrom}
                    onSelect={setDateFrom}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="date-to">
                {language === "ar" ? "إلى تاريخ" : "To Date"}
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !dateTo && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateTo ? format(dateTo, "PPP", { locale: language === "ar" ? ar : enUS }) : 
                     (language === "ar" ? "اختر تاريخ" : "Pick a date")}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={dateTo}
                    onSelect={setDateTo}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            {hasActiveFilters && (
              <div className="lg:col-span-5 flex justify-end">
                <Button variant="ghost" onClick={clearFilters}>
                  {language === "ar" ? "مسح الفلاتر" : "Clear Filters"}
                </Button>
              </div>
            )}
          </div>
        )}
      </CardHeader>
      <CardContent>
        {services.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>{language === "ar" ? "لا توجد خدمات قادمة" : "No upcoming services"}</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === "ar" ? "رقم اللوحة" : "Plate Number"}</TableHead>
                <TableHead>{language === "ar" ? "نوع الخدمة" : "Service Type"}</TableHead>
                <TableHead>{language === "ar" ? "ال_TRIGGER" : "Trigger"}</TableHead>
                <TableHead>{language === "ar" ? "المتبقي" : "Remaining"}</TableHead>
                <TableHead>{language === "ar" ? "الأولوية" : "Priority"}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {services.map((service, index) => {
                const daysUntilDue = getDaysUntilDue(service.due_date)
                const remainingKm = getRemainingKm(service.remaining_km)
                
                return (
                  <TableRow key={`${service.vehicle_id}-${index}`}>
                    <TableCell className="font-medium">{service.plate_number}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getServiceIcon(service.service_type)}
                        <span>{getServiceTypeLabel(service.service_type, service.service_type_ar)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {service.due_date ? (
                        <div className="flex items-center gap-1">
                          <CalendarIcon className="h-3 w-3" />
                          <span>{new Date(service.due_date).toLocaleDateString(language === "ar" ? "ar-EG" : "en-EG")}</span>
                        </div>
                      ) : service.trigger_value ? (
                        <div className="flex items-center gap-1">
                          <span>{service.trigger_value?.toLocaleString()}</span>
                          <span className="text-muted-foreground text-xs">km</span>
                        </div>
                      ) : (
                        "-"
                      )}
                    </TableCell>
                    <TableCell>
                      {service.due_date && daysUntilDue !== null ? (
                        <div className="flex items-center gap-1">
                          <span>{daysUntilDue}</span>
                          <span className="text-muted-foreground text-xs">
                            {language === "ar" ? "يوم" : "days"}
                          </span>
                        </div>
                      ) : service.remaining_km !== null ? (
                        <div className="flex items-center gap-1">
                          <span>{service.remaining_km?.toLocaleString()}</span>
                          <span className="text-muted-foreground text-xs">km</span>
                        </div>
                      ) : (
                        "-"
                      )}
                    </TableCell>
                    <TableCell>
                      {getPriorityBadge(daysUntilDue, remainingKm)}
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}