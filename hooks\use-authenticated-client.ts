"use client"

import { createClient } from '@/lib/supabase-browser'
import { useAuth } from '@/contexts/auth-context'
import { useEffect, useState, useCallback } from 'react'
import type { SupabaseClient } from '@supabase/supabase-js'

/**
 * Custom hook that provides an authenticated Supabase client
 * Uses singleton pattern to prevent unnecessary client recreations
 */
export function useSupabaseClient(): SupabaseClient | null {
  const [client, setClient] = useState<SupabaseClient | null>(null)

  useEffect(() => {
    // Create client once on mount
    const supabaseClient = createClient()
    setClient(supabaseClient)
  }, []) // Empty dependency array - only run once

  return client
}

/**
 * Hook for authenticated database operations with error handling
 */
export function useAuthenticatedOperation() {
  const { user, isLoading } = useAuth()
  const client = useSupabaseClient()

  const executeOperation = useCallback(async <T>(
    operation: (client: SupabaseClient) => Promise<T>
  ): Promise<T> => {
    if (isLoading) {
      throw new Error('Authentication is still loading')
    }

    if (!user) {
      throw new Error('User not authenticated')
    }

    if (!client) {
      throw new Error('Supabase client not available')
    }

    try {
      const result = await operation(client)
      return result
    } catch (error: any) {
      // Enhanced error logging for authentication issues
      console.error('=== AUTHENTICATED OPERATION ERROR ===', {
        timestamp: new Date().toISOString(),
        user: user?.email,
        userId: user?.id,
        error: error,
        errorMessage: error?.message,
        isAuthError: error?.name?.includes('Auth'),
        stack: error?.stack
      })
      throw error
    }
  }, [isLoading, user?.id, client]) // Use user.id to prevent unnecessary re-renders

  return {
    executeOperation,
    client,
    user,
    isAuthenticated: !!user && !isLoading,
    isLoading
  }
}