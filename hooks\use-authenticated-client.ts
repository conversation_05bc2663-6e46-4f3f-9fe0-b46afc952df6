"use client"

import { createClient } from '@/lib/supabase-browser'
import { useAuth } from '@/contexts/auth-context'
import { useEffect, useState } from 'react'
import type { SupabaseClient } from '@supabase/supabase-js'

/**
 * Custom hook that provides an authenticated Supabase client
 * Ensures all database operations use the proper user session
 */
export function useSupabaseClient(): SupabaseClient | null {
  const { user, isLoading } = useAuth()
  const [client, setClient] = useState<SupabaseClient | null>(null)

  useEffect(() => {
    if (!isLoading) {
      const supabaseClient = createClient()
      setClient(supabaseClient)
    }
  }, [isLoading, user])

  return client
}

/**
 * Hook for authenticated database operations with error handling
 */
export function useAuthenticatedOperation() {
  const { user, isLoading } = useAuth()
  const client = useSupabaseClient()

  const executeOperation = async <T>(
    operation: (client: SupabaseClient) => Promise<T>
  ): Promise<T> => {
    if (isLoading) {
      throw new Error('Authentication is still loading')
    }

    if (!user) {
      throw new Error('User not authenticated')
    }

    if (!client) {
      throw new Error('Supabase client not available')
    }

    try {
      const result = await operation(client)
      return result
    } catch (error: any) {
      // Enhanced error logging for authentication issues
      console.error('=== AUTHENTICATED OPERATION ERROR ===', {
        timestamp: new Date().toISOString(),
        user: user?.email,
        userId: user?.id,
        error: error,
        errorMessage: error?.message,
        isAuthError: error?.name?.includes('Auth'),
        stack: error?.stack
      })
      throw error
    }
  }

  return {
    executeOperation,
    client,
    user,
    isAuthenticated: !!user && !isLoading,
    isLoading
  }
}