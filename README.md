# Fleetlytic - Fleet Management Dashboard

A comprehensive fleet management dashboard built with Next.js, React, and Supabase.

## 🚀 Quick Start

### Development
```bash
npm install
npm run dev
```

### Production Build
```bash
npm run build:production
```

## 🔧 Troubleshooting

### Infinite Loading Issue Fixed
If you experienced infinite loading on the dashboard page, this was caused by:

1. **Static Export Configuration**: The `output: 'export'` setting in `next.config.mjs` was preventing proper client-side functionality
2. **Route Conflicts**: Having both `app/page.tsx` and `app/(dashboard)/page.tsx` created routing conflicts
3. **Redirect Loops**: Improper authentication redirects caused infinite loops

**Solution Applied:**
- Removed static export configuration for development
- Deleted conflicting `app/page.tsx` file
- Fixed authentication flow in dashboard layout
- Separated development and production configurations

### For Vercel Deployment
Use the production build script which applies the correct configuration:
```bash
npm run vercel-build
```

### Additional Issues Fixed
- **Missing Avatar Images**: Removed references to non-existent avatar images and used fallback initials
- **404 Errors for .txt Files**: These are generated by Next.js static export and are normal for prefetching
- **Console Errors**: Cleaned up unused imports and variables

## Project Overview

Fleetlytic is a modern web application designed to provide comprehensive monitoring, management, and analytics for vehicle fleets. It enables organizations to streamline operations across key areas such as maintenance, fuel usage, GPS tracking, driver management, financial reporting, and user access control.

## Features

- Dashboard with executive summary and KPIs
- Alerts & Diagnostics for real-time vehicle health
- Fleet & Vehicle Management
- Maintenance scheduling and tracking
- Fuel consumption monitoring
- GPS tracking and route visualization
- Driver and user management
- Financial reporting and analytics
- Role-based access control via Supabase authentication
- Exportable reports and data visualization
- **Arabic Language Support with Cairo Font**: Full RTL support and proper Arabic font rendering in PDF exports

## Technology Stack

- **Frontend**: React 19, Next.js 15.2.4, TypeScript
- **Styling**: Tailwind CSS, Radix UI primitives
- **State Management**: React Context, custom hooks
- **Forms**: React Hook Form, Zod validation
- **Charts**: Recharts
- **Auth & Backend**: Supabase
- **UI Components**: Custom shadcn-style components
- **Internationalization**: Built-in Arabic/English support with Cairo font for PDF exports

## Getting Started

### Prerequisites

- Node.js (version 18 or higher)
- pnpm (package manager)
- Supabase account

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd fleetlytic
   ```

2. Install dependencies:
   ```bash
   pnpm install
   ```

3. Set up environment variables:
   Create a `.env.local` file with your Supabase credentials:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   ```

4. Run the development server:
   ```bash
   pnpm dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Deployment

### Deployment to Vercel

This project is configured for static export and can be deployed to Vercel.

1. Build the application:
   ```bash
   npm run build
   ```

2. Deploy using Vercel CLI:
   ```bash
   vercel --prod
   ```

Alternatively, you can use the deployment scripts:
- On Windows: Run `deploy.bat`
- On macOS/Linux: Run `deploy.sh`

### Manual Deployment Steps

1. Push your code to a GitHub repository
2. Log in to your Vercel account
3. Click "New Project" and import your repository
4. Set the project name to "fleetlytic"
5. Vercel will automatically detect the Next.js framework
6. Add the required environment variables in the Vercel project settings:
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - `SUPABASE_SERVICE_ROLE_KEY`
7. Deploy the project

For more detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

## Arabic Font Support

This application includes comprehensive Arabic language support with the Cairo font for both web display and PDF exports:

- **Web Display**: Cairo font loaded via Google Fonts for optimal Arabic text rendering
- **PDF Exports**: Special handling for Arabic text in PDFs with proper RTL support
- **Bilingual Interface**: Seamless switching between Arabic and English interfaces
- **RTL Layout**: Full right-to-left layout support for Arabic content

For technical details on how Arabic font support is implemented, see [docs/arabic-font-pdf-guide.md](docs/arabic-font-pdf-guide.md).

## Available Scripts

- `pnpm dev` - Runs the app in development mode
- `pnpm build` - Builds the app for production
- `pnpm start` - Runs the built app in production mode
- `pnpm lint` - Runs the linter

## Learn More

To learn more about the technologies used in this project:

- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://reactjs.org/)
- [Supabase Documentation](https://supabase.io/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/)