"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Bar<PERSON>hart3, <PERSON><PERSON><PERSON>, TrendingUp, Activity } from "lucide-react"
import { useFuelTrends, useMaintenanceCosts, useVehicleStatusDistribution } from "@/hooks/use-fleet-data"
import { LoadingSpinner } from "@/components/loading-spinner"
import { ErrorDisplay } from "@/components/error-display"

interface AdvancedVisualizationsProps {
  language: "ar" | "en"
}

export function AdvancedVisualizations({ language }: AdvancedVisualizationsProps) {
  const { data: fuelTrends, loading: fuelLoading, error: fuelError } = useFuelTrends(7)
  const { data: maintenanceCosts, loading: maintenanceLoading, error: maintenanceError } = useMaintenanceCosts(6)
  const { data: statusDistribution, loading: statusLoading, error: statusError } = useVehicleStatusDistribution()

  if (fuelLoading || maintenanceLoading || statusLoading) {
    return <LoadingSpinner className="h-64" />
  }

  if (fuelError || maintenanceError || statusError) {
    return <ErrorDisplay error={fuelError || maintenanceError || statusError || "Unknown error"} />
  }

  // Mock data for demonstration
  const mockFuelTrends = [
    { fuel_date: "2024-01-15", total_liters: 1500, total_cost_egp: 10500 },
    { fuel_date: "2024-01-16", total_liters: 1700, total_cost_egp: 11900 },
    { fuel_date: "2024-01-17", total_liters: 1600, total_cost_egp: 11200 },
    { fuel_date: "2024-01-18", total_liters: 1800, total_cost_egp: 12600 },
    { fuel_date: "2024-01-19", total_liters: 1650, total_cost_egp: 11550 },
    { fuel_date: "2024-01-20", total_liters: 1750, total_cost_egp: 12250 },
    { fuel_date: "2024-01-21", total_liters: 1900, total_cost_egp: 13300 },
  ]

  const mockStatusDistribution = [
    { status: "active", count: 142, percentage: 85 },
    { status: "maintenance", count: 12, percentage: 8 },
    { status: "out_of_service", count: 8, percentage: 5 },
    { status: "retired", count: 3, percentage: 2 },
  ]

  const fuelData = fuelTrends.length > 0 ? fuelTrends : mockFuelTrends
  const statusData = statusDistribution.length > 0 ? statusDistribution : mockStatusDistribution

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-EG", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    const colors = {
      active: "bg-green-500",
      maintenance: "bg-orange-500",
      out_of_service: "bg-red-500",
      retired: "bg-gray-500",
    }
    return colors[status as keyof typeof colors] || "bg-gray-500"
  }

  const getStatusLabel = (status: string) => {
    const labels = {
      active: language === "ar" ? "نشط" : "Active",
      maintenance: language === "ar" ? "صيانة" : "Maintenance",
      out_of_service: language === "ar" ? "خارج الخدمة" : "Out of Service",
      retired: language === "ar" ? "متقاعد" : "Retired",
    }
    return labels[status as keyof typeof labels] || status
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">
          {language === "ar" ? "التحليلات المتقدمة" : "Advanced Analytics"}
        </h2>
        <p className="text-muted-foreground">
          {language === "ar" ? "رؤى تفصيلية حول أداء الأسطول" : "Detailed insights into fleet performance"}
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Fuel Trends Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {language === "ar" ? "اتجاهات استهلاك الوقود" : "Fuel Consumption Trends"}
            </CardTitle>
            <CardDescription>{language === "ar" ? "آخر 7 أيام" : "Last 7 days"}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {fuelData.map((day, index) => {
                const maxCost = Math.max(...fuelData.map((d) => d.total_cost_egp))
                const percentage = (day.total_cost_egp / maxCost) * 100

                return (
                  <div key={day.fuel_date} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{new Date(day.fuel_date).toLocaleDateString(language === "ar" ? "ar-EG" : "en-EG")}</span>
                      <span className="font-medium">{formatCurrency(day.total_cost_egp)}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {day.total_liters.toLocaleString()} {language === "ar" ? "لتر" : "liters"}
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Vehicle Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              {language === "ar" ? "توزيع حالة المركبات" : "Vehicle Status Distribution"}
            </CardTitle>
            <CardDescription>
              {language === "ar" ? "التوزيع الحالي للمركبات" : "Current vehicle distribution"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {statusData.map((item) => (
                <div key={item.status} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(item.status)}`} />
                    <span className="text-sm font-medium">{getStatusLabel(item.status)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">{item.count}</span>
                    <Badge variant="outline">{item.percentage}%</Badge>
                  </div>
                </div>
              ))}
            </div>

            {/* Simple visual representation */}
            <div className="mt-6">
              <div className="flex h-4 rounded-full overflow-hidden">
                {statusData.map((item) => (
                  <div
                    key={item.status}
                    className={getStatusColor(item.status)}
                    style={{ width: `${item.percentage}%` }}
                  />
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Indicators */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "متوسط استهلاك الوقود اليومي" : "Daily Fuel Consumption"}
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(fuelData.reduce((sum, day) => sum + day.total_liters, 0) / fuelData.length).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">{language === "ar" ? "لتر يومياً" : "liters per day"}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "متوسط التكلفة اليومية" : "Daily Cost Average"}
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(Math.round(fuelData.reduce((sum, day) => sum + day.total_cost_egp, 0) / fuelData.length))}
            </div>
            <p className="text-xs text-muted-foreground">{language === "ar" ? "متوسط يومي" : "daily average"}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "معدل توفر الأسطول" : "Fleet Availability"}
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {statusData.find((s) => s.status === "active")?.percentage || 0}%
            </div>
            <p className="text-xs text-muted-foreground">{language === "ar" ? "مركبات متاحة" : "vehicles available"}</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
