import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Utility function to get font class based on language
export function getFontClass(language: string): string {
  return language === "ar" ? "font-arabic" : "font-english"
}

// Utility function to combine cn with font class
export function cnWithFont(language: string, ...inputs: ClassValue[]) {
  return cn(getFontClass(language), ...inputs)
}

// Utility function to handle RTL-aware spacing and positioning
export function cnWithDirection(isRTL: boolean, ...inputs: ClassValue[]) {
  return cn(isRTL ? "rtl" : "ltr", ...inputs)
}

// Utility function to get appropriate icon margin class based on direction
export function getIconMargin(isRTL: boolean, position: 'left' | 'right' = 'right') {
  if (position === 'right') {
    return isRTL ? "ml-2" : "mr-2"
  } else {
    return isRTL ? "mr-2" : "ml-2"
  }
}

// Utility function to handle icon flipping for RTL
export function getIconClass(isRTL: boolean, baseClass: string = "", noFlip: boolean = false) {
  const flipClass = isRTL && !noFlip ? "rtl-flip" : ""
  return cn(baseClass, flipClass)
}
