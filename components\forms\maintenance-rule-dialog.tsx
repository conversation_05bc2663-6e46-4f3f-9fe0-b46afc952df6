"use client"

import type React from "react"
import { useState, useEffect, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Plus, Edit } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useAuthenticatedOperation } from "@/hooks/use-authenticated-client"

interface MaintenanceRuleFormData {
  code: string
  vehicle_type: string
  fuel_type: string
  check_type: string
  service_type: string
  value: string
}

interface MaintenanceRuleDialogProps {
  language: "ar" | "en"
  onRuleAdded?: () => void
  rule?: any // For edit mode
  open?: boolean // Control dialog open state from parent
  onOpenChange?: (open: boolean) => void // Notify parent of open state changes
  initialFormData?: MaintenanceRuleFormData // Initial form data for add mode
  onFormDataChange?: (data: MaintenanceRuleFormData) => void // Notify parent of form data changes
}

export function MaintenanceRuleDialog({ 
  language, 
  onRuleAdded, 
  rule, 
  open: controlledOpen, 
  onOpenChange,
  initialFormData,
  onFormDataChange
}: MaintenanceRuleDialogProps) {
  // Use controlled or uncontrolled state based on whether open prop is provided
  const isControlled = controlledOpen !== undefined
  const [uncontrolledOpen, setUncontrolledOpen] = useState(false)
  const open = isControlled ? controlledOpen : uncontrolledOpen
  const setOpen = isControlled ? onOpenChange : setUncontrolledOpen
  
  const [loading, setLoading] = useState(false)
  const { executeOperation } = useAuthenticatedOperation()
  const { toast } = useToast()

  // Memoize initial form data to prevent unnecessary re-renders
  const initialData = useMemo(() => ({
    code: rule?.code || initialFormData?.code || "",
    vehicle_type: rule?.vehicle_type || initialFormData?.vehicle_type || "",
    fuel_type: rule?.fuel_type || initialFormData?.fuel_type || "",
    check_type: rule?.check_type || initialFormData?.check_type || "",
    service_type: rule?.service_type || initialFormData?.service_type || "",
    value: rule?.value?.toString() || initialFormData?.value || "",
  }), [rule, initialFormData]) // Include rule and initialFormData in dependencies

  const [formData, setFormData] = useState<MaintenanceRuleFormData>(initialData)

  // Update form data when rule changes (for edit mode) - only when rule actually changes
  useEffect(() => {
    if (rule) {
      setFormData({
        code: rule.code || "",
        vehicle_type: rule.vehicle_type || "",
        fuel_type: rule.fuel_type || "",
        check_type: rule.check_type || "",
        service_type: rule.service_type || "",
        value: rule.value?.toString() || "",
      })
    }
  }, [rule?.id]) // Only depend on rule ID to prevent infinite loops

  // Notify parent of form data changes if callback provided
  useEffect(() => {
    if (onFormDataChange && !rule) { // Only for add mode
      onFormDataChange(formData)
    }
  }, [formData, onFormDataChange, rule])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const ruleData = {
        ...formData,
        value: parseInt(formData.value) || 0,
      }

      if (rule) {
        // Edit mode
        await executeOperation(async (client) => {
          const { error } = await client
            .from("maintenance_rules")
            .update(ruleData)
            .eq("id", rule.id)

          if (error) throw error
        })

        toast({
          title: language === "ar" ? "تم تحديث القاعدة" : "Rule Updated",
          description: language === "ar" ? "تم تحديث قاعدة الصيانة بنجاح" : "Maintenance rule has been updated successfully",
        })
      } else {
        // Add mode
        await executeOperation(async (client) => {
          const { error } = await client
            .from("maintenance_rules")
            .insert([ruleData])

          if (error) throw error
        })

        toast({
          title: language === "ar" ? "تم إضافة القاعدة" : "Rule Added",
          description: language === "ar" ? "تم إضافة قاعدة الصيانة بنجاح" : "Maintenance rule has been added successfully",
        })
      }

      // Reset form and close dialog
      setFormData({
        code: "",
        vehicle_type: "",
        fuel_type: "",
        check_type: "",
        service_type: "",
        value: "",
      })
      
      // Close dialog
      if (setOpen) {
        setOpen(false)
      }

      // Trigger refresh if callback provided
      if (onRuleAdded) {
        onRuleAdded()
      }
    } catch (err) {
      console.error("Error saving maintenance rule:", err)
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description: language === "ar" ? "حدث خطأ أثناء حفظ القاعدة" : "An error occurred while saving the rule",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Handle internal open state changes
  const handleOpenChange = (newOpen: boolean) => {
    if (setOpen) {
      setOpen(newOpen)
    }
  }

  // Handle form data changes
  const handleFormDataChange = (newData: Partial<MaintenanceRuleFormData>) => {
    setFormData(prev => ({ ...prev, ...newData }))
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {rule ? (
          <Button variant="ghost" size="sm">
            <Edit className="h-4 w-4" />
          </Button>
        ) : (
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            {language === "ar" ? "إضافة قاعدة" : "Add Rule"}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {rule 
              ? (language === "ar" ? "تعديل قاعدة الصيانة" : "Edit Maintenance Rule")
              : (language === "ar" ? "إضافة قاعدة صيانة جديدة" : "Add New Maintenance Rule")}
          </DialogTitle>
          <DialogDescription>
            {language === "ar" ? "أدخل تفاصيل قاعدة الصيانة" : "Enter the maintenance rule details"}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="code">{language === "ar" ? "الرمز" : "Code"} *</Label>
              <Input
                id="code"
                value={formData.code}
                onChange={(e) => handleFormDataChange({ code: e.target.value })}
                placeholder={language === "ar" ? "رمز القاعدة" : "Rule code"}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="vehicle_type">{language === "ar" ? "نوع المركبة" : "Vehicle Type"}</Label>
                <Input
                  id="vehicle_type"
                  value={formData.vehicle_type}
                  onChange={(e) => handleFormDataChange({ vehicle_type: e.target.value })}
                  placeholder={language === "ar" ? "نوع المركبة" : "Vehicle type"}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="fuel_type">{language === "ar" ? "نوع الوقود" : "Fuel Type"}</Label>
                <Input
                  id="fuel_type"
                  value={formData.fuel_type}
                  onChange={(e) => handleFormDataChange({ fuel_type: e.target.value })}
                  placeholder={language === "ar" ? "نوع الوقود" : "Fuel type"}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="check_type">{language === "ar" ? "نوع الفحص" : "Check Type"} *</Label>
                <Input
                  id="check_type"
                  value={formData.check_type}
                  onChange={(e) => handleFormDataChange({ check_type: e.target.value })}
                  placeholder={language === "ar" ? "نوع الفحص" : "Check type"}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="service_type">{language === "ar" ? "نوع الخدمة" : "Service Type"} *</Label>
                <Input
                  id="service_type"
                  value={formData.service_type}
                  onChange={(e) => handleFormDataChange({ service_type: e.target.value })}
                  placeholder={language === "ar" ? "نوع الخدمة" : "Service type"}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="value">{language === "ar" ? "القيمة" : "Value"} *</Label>
              <Input
                id="value"
                type="number"
                value={formData.value}
                onChange={(e) => handleFormDataChange({ value: e.target.value })}
                placeholder={language === "ar" ? "القيمة (كم أو أيام)" : "Value (km or days)"}
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => handleOpenChange(false)}>
              {language === "ar" ? "إلغاء" : "Cancel"}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 
                (language === "ar" ? "جاري الحفظ..." : "Saving...") : 
                (rule ? (language === "ar" ? "تحديث" : "Update") : (language === "ar" ? "إضافة" : "Add"))
              }
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}