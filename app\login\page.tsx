"use client"

import { useState, Suspense, useEffect } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Eye, EyeOff, Loader2, Languages, Car } from "lucide-react"
import { useLanguage } from "@/contexts/language-context"
import { useAuth } from "@/contexts/auth-context"
import { ModeToggle } from "@/components/mode-toggle"
import { cnWithFont, getIconMargin } from "@/lib/utils"

function LoginForm() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { t, language, setLanguage, isRTL } = useLanguage()
  const { signIn, isLoading, user } = useAuth()
  
  // Redirect to dashboard if already logged in
  useEffect(() => {
    if (user) {
      router.push('/')
    }
  }, [user, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    try {
      await signIn(email, password)
      // Navigation is now handled by the useEffect in this component
      // and by the immediate state update in the auth context
    } catch (error: any) {
      console.error('Login error:', error)
      setError(error.message)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className={`absolute top-4 flex items-center gap-2 ${isRTL ? 'left-4' : 'right-4'}`}>
        <Button variant="outline" size="sm" onClick={() => setLanguage(language === "ar" ? "en" : "ar")}>
          <Languages className={`h-4 w-4 ${getIconMargin(isRTL)}`} />
          <span className={cnWithFont(language)}>
            {language === "ar" ? "English" : "العربية"}
          </span>
        </Button>
        <ModeToggle />
      </div>
      <div className="w-full max-w-md px-4">
        <div className="text-center mb-4">
          <div className="flex items-center justify-center mb-2">
            <div className="flex aspect-square size-12 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <Car className="size-6" />
            </div>
          </div>
          <h1 className={cnWithFont(language, "text-2xl font-bold text-foreground mb-1")}>
            {t("login.title")}
          </h1>
        </div>

        <Card className="shadow-lg">
          <CardHeader className="space-y-1">
            <div className={cnWithFont(language, "text-center mb-1")}>
              <p className={cnWithFont(language, "text-lg font-medium text-muted-foreground")}>
                {t("login.welcome")}
              </p>
            </div>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-2">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-1">
                <Label htmlFor="email" className={cnWithFont(language)}>{t("login.email")}</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder={t("login.emailPlaceholder")}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="h-11"
                  dir={t("common.dir") as "ltr" | "rtl"}
                />
              </div>

              <div className="space-y-1">
                <Label htmlFor="password" className={cnWithFont(language)}>{t("login.password")}</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder={t("login.passwordPlaceholder")}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="h-11 pr-10"
                    dir={t("common.dir") as "ltr" | "rtl"}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 end-0 flex items-center pr-3"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </button>
                </div>
              </div>

              <Button
                type="submit"
                className={cnWithFont(language, "w-full h-11 mt-6")}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("login.signingIn")}
                  </>
                ) : (
                  t("login.signIn")
                )}
              </Button>
            </CardContent>
          </form>
        </Card>
      </div>
    </div>
  )
}

export default function LoginPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    }>
      <LoginForm />
    </Suspense>
  )
}