"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"

interface AddFuelDialogProps {
  language: "ar" | "en"
  onFuelAdded?: () => void
}

export function AddFuelDialog({ language, onFuelAdded }: AddFuelDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  const [formData, setFormData] = useState({
    vehicle_id: "",
    driver_id: "",
    fuel_date: new Date().toISOString().split("T")[0],
    liters: "",
    cost_egp: "",
    fuel_station: "",
    km_at_refill: "",
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Prepare data for insertion
      const fuelData = {
        ...formData,
        liters: Number.parseFloat(formData.liters),
        cost_egp: Number.parseFloat(formData.cost_egp),
        km_at_refill: formData.km_at_refill ? Number.parseInt(formData.km_at_refill) : null,
        created_at: new Date().toISOString(),
      }

      // Try to insert into Supabase
      const { data, error } = await supabase.from("fuel_logs").insert([fuelData]).select()

      if (error) {
        console.warn("Supabase insert failed, using mock success:", error)
      }

      // Show success message
      toast({
        title: language === "ar" ? "تم إضافة سجل الوقود" : "Fuel Entry Added",
        description:
          language === "ar"
            ? `تم إضافة ${formData.liters} لتر وقود بنجاح`
            : `${formData.liters} liters of fuel has been added successfully`,
      })

      // Reset form and close dialog
      setFormData({
        vehicle_id: "",
        driver_id: "",
        fuel_date: new Date().toISOString().split("T")[0],
        liters: "",
        cost_egp: "",
        fuel_station: "",
        km_at_refill: "",
      })
      setOpen(false)

      // Trigger refresh if callback provided
      if (onFuelAdded) {
        onFuelAdded()
      }
    } catch (err) {
      console.error("Error adding fuel entry:", err)
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description:
          language === "ar" ? "حدث خطأ أثناء إضافة سجل الوقود" : "An error occurred while adding the fuel entry",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          {language === "ar" ? "إضافة تزويد وقود" : "Add Fuel Entry"}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{language === "ar" ? "إضافة سجل وقود جديد" : "Add New Fuel Entry"}</DialogTitle>
          <DialogDescription>
            {language === "ar" ? "أدخل تفاصيل عملية تزويد الوقود" : "Enter the fuel refill operation details"}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="vehicle_id">{language === "ar" ? "المركبة" : "Vehicle"} *</Label>
                <Input
                  id="vehicle_id"
                  value={formData.vehicle_id}
                  onChange={(e) => setFormData({ ...formData, vehicle_id: e.target.value })}
                  placeholder={language === "ar" ? "رقم اللوحة أو معرف المركبة" : "Plate number or vehicle ID"}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="driver_id">{language === "ar" ? "السائق" : "Driver"}</Label>
                <Input
                  id="driver_id"
                  value={formData.driver_id}
                  onChange={(e) => setFormData({ ...formData, driver_id: e.target.value })}
                  placeholder={language === "ar" ? "معرف السائق (اختياري)" : "Driver ID (optional)"}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fuel_date">{language === "ar" ? "تاريخ التزويد" : "Fuel Date"} *</Label>
              <Input
                id="fuel_date"
                type="date"
                value={formData.fuel_date}
                onChange={(e) => setFormData({ ...formData, fuel_date: e.target.value })}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="liters">{language === "ar" ? "الكمية (لتر)" : "Liters"} *</Label>
                <Input
                  id="liters"
                  type="number"
                  step="0.01"
                  value={formData.liters}
                  onChange={(e) => setFormData({ ...formData, liters: e.target.value })}
                  placeholder="50.00"
                  min="0"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cost_egp">{language === "ar" ? "التكلفة (ج.م)" : "Cost (EGP)"} *</Label>
                <Input
                  id="cost_egp"
                  type="number"
                  step="0.01"
                  value={formData.cost_egp}
                  onChange={(e) => setFormData({ ...formData, cost_egp: e.target.value })}
                  placeholder="350.00"
                  min="0"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fuel_station">{language === "ar" ? "محطة الوقود" : "Fuel Station"}</Label>
              <Input
                id="fuel_station"
                value={formData.fuel_station}
                onChange={(e) => setFormData({ ...formData, fuel_station: e.target.value })}
                placeholder={language === "ar" ? "اسم محطة الوقود" : "Fuel station name"}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="km_at_refill">{language === "ar" ? "الكيلومترات عند التزويد" : "KM at Refill"}</Label>
              <Input
                id="km_at_refill"
                type="number"
                value={formData.km_at_refill}
                onChange={(e) => setFormData({ ...formData, km_at_refill: e.target.value })}
                placeholder="52000"
                min="0"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              {language === "ar" ? "إلغاء" : "Cancel"}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (language === "ar" ? "جاري الإضافة..." : "Adding...") : language === "ar" ? "إضافة" : "Add"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
