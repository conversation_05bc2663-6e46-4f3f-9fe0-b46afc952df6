"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Car, Fuel, TrendingUp, AlertTriangle, DollarSign } from "lucide-react"
import { useFleetKPIs, useFleetOverview } from "@/hooks/use-fleet-data"
import { LoadingSpinner } from "@/components/loading-spinner"
import { ErrorDisplay } from "@/components/error-display"

interface ExecutiveSummaryProps {
  language: "ar" | "en"
}

export function ExecutiveSummary({ language }: ExecutiveSummaryProps) {
  const { data: kpis, loading: kpisLoading, error: kpisError } = useFleetKPIs()
  const { data: overview, loading: overviewLoading, error: overviewError } = useFleetOverview()

  if (kpisLoading || overviewLoading) {
    return <LoadingSpinner className="h-64" />
  }

  if (kpisError || overviewError) {
    return <ErrorDisplay error={kpisError || overviewError || "Unknown error"} />
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-EG", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  // Mock data for demonstration
  const mockKPIs = {
    total_vehicles: 150,
    active_vehicles: 142,
    monthly_operating_cost: 125000,
    fuel_efficiency_km_per_liter: 8.5,
    cost_per_kilometer: 2.3,
    fleet_utilization_rate: 85.2,
    driver_performance: 87.5,
    overdue_maintenance: 8,
    maintenance_compliance: 92.1,
    roi_percentage: 15.8,
  }

  const data = kpis || mockKPIs

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">
          {language === "ar" ? "الملخص التنفيذي" : "Executive Summary"}
        </h2>
        <p className="text-muted-foreground">
          {language === "ar" ? "نظرة عامة على أداء الأسطول" : "Overview of fleet performance"}
        </p>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "إجمالي المركبات" : "Total Vehicles"}
            </CardTitle>
            <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.total_vehicles}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">{data.active_vehicles}</span> {language === "ar" ? "نشط" : "active"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "التكلفة الشهرية" : "Monthly Cost"}
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(data.monthly_operating_cost)}</div>
            <p className="text-xs text-muted-foreground">
              {formatCurrency(data.cost_per_kilometer)} {language === "ar" ? "لكل كم" : "per km"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "كفاءة الوقود" : "Fuel Efficiency"}
            </CardTitle>
            <Fuel className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.fuel_efficiency_km_per_liter}</div>
            <p className="text-xs text-muted-foreground">{language === "ar" ? "كم/لتر" : "km/liter"}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "الصيانة المتأخرة" : "Overdue Maintenance"}
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">{data.overdue_maintenance}</div>
            <p className="text-xs text-muted-foreground">{language === "ar" ? "مركبات" : "vehicles"}</p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">
              {language === "ar" ? "معدل استخدام الأسطول" : "Fleet Utilization Rate"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Progress value={data.fleet_utilization_rate} className="flex-1" />
              <span className="text-sm font-medium">{data.fleet_utilization_rate}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">{language === "ar" ? "أداء السائقين" : "Driver Performance"}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Progress value={data.driver_performance} className="flex-1" />
              <span className="text-sm font-medium">{data.driver_performance}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">
              {language === "ar" ? "الامتثال للصيانة" : "Maintenance Compliance"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Progress value={data.maintenance_compliance} className="flex-1" />
              <span className="text-sm font-medium">{data.maintenance_compliance}%</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ROI Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            {language === "ar" ? "العائد على الاستثمار" : "Return on Investment"}
          </CardTitle>
          <CardDescription>
            {language === "ar" ? "الأداء المالي للأسطول" : "Financial performance of the fleet"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-green-600">{data.roi_percentage}%</div>
          <p className="text-sm text-muted-foreground mt-2">
            {language === "ar" ? "زيادة بنسبة 2.3% عن الشهر الماضي" : "2.3% increase from last month"}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
