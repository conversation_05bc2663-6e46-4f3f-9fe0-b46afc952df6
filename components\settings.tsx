"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { SettingsIcon, User, Bell, Shield, Database } from "lucide-react"

interface SettingsProps {
  language: "ar" | "en"
}

export function Settings({ language }: SettingsProps) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">{language === "ar" ? "الإعدادات" : "Settings"}</h2>
        <p className="text-muted-foreground">
          {language === "ar" ? "إدارة إعدادات النظام والتفضيلات" : "Manage system settings and preferences"}
        </p>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">{language === "ar" ? "عام" : "General"}</TabsTrigger>
          <TabsTrigger value="notifications">{language === "ar" ? "الإشعارات" : "Notifications"}</TabsTrigger>
          <TabsTrigger value="security">{language === "ar" ? "الأمان" : "Security"}</TabsTrigger>
          <TabsTrigger value="data">{language === "ar" ? "البيانات" : "Data"}</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SettingsIcon className="h-5 w-5" />
                  {language === "ar" ? "الإعدادات العامة" : "General Settings"}
                </CardTitle>
                <CardDescription>
                  {language === "ar" ? "إعدادات النظام الأساسية" : "Basic system settings"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="company-name">{language === "ar" ? "اسم الشركة" : "Company Name"}</Label>
                    <Input id="company-name" defaultValue="Advanced Transport Co." />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="timezone">{language === "ar" ? "المنطقة الزمنية" : "Timezone"}</Label>
                    <Select defaultValue="asia/riyadh">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="asia/riyadh">Asia/Riyadh (GMT+3)</SelectItem>
                        <SelectItem value="utc">UTC (GMT+0)</SelectItem>
                        <SelectItem value="europe/london">Europe/London (GMT+0)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="currency">{language === "ar" ? "العملة" : "Currency"}</Label>
                    <Select defaultValue="egp">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="egp">Egyptian Pound (EGP)</SelectItem>
                        <SelectItem value="sar">Saudi Riyal (SAR)</SelectItem>
                        <SelectItem value="usd">US Dollar (USD)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="language-select">{language === "ar" ? "اللغة" : "Language"}</Label>
                    <Select defaultValue="en">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="en">English</SelectItem>
                        <SelectItem value="ar">العربية</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  {language === "ar" ? "معلومات المستخدم" : "User Information"}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="full-name">{language === "ar" ? "الاسم الكامل" : "Full Name"}</Label>
                    <Input id="full-name" defaultValue="Ahmed Hassan" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">{language === "ar" ? "البريد الإلكتروني" : "Email"}</Label>
                    <Input id="email" type="email" defaultValue="<EMAIL>" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role">{language === "ar" ? "الدور" : "Role"}</Label>
                  <Select defaultValue="admin">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">{language === "ar" ? "مدير النظام" : "System Admin"}</SelectItem>
                      <SelectItem value="fleet_manager">
                        {language === "ar" ? "مدير الأسطول" : "Fleet Manager"}
                      </SelectItem>
                      <SelectItem value="operator">{language === "ar" ? "مشغل" : "Operator"}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                {language === "ar" ? "إعدادات الإشعارات" : "Notification Settings"}
              </CardTitle>
              <CardDescription>
                {language === "ar" ? "إدارة تفضيلات الإشعارات" : "Manage notification preferences"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{language === "ar" ? "تنبيهات الصيانة" : "Maintenance Alerts"}</Label>
                  <p className="text-sm text-muted-foreground">
                    {language === "ar" ? "إشعارات عند استحقاق الصيانة" : "Notifications when maintenance is due"}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{language === "ar" ? "تنبيهات الوقود" : "Fuel Alerts"}</Label>
                  <p className="text-sm text-muted-foreground">
                    {language === "ar" ? "إشعارات عند انخفاض مستوى الوقود" : "Notifications for low fuel levels"}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{language === "ar" ? "تنبيهات السرعة" : "Speed Alerts"}</Label>
                  <p className="text-sm text-muted-foreground">
                    {language === "ar" ? "إشعارات عند تجاوز السرعة المحددة" : "Notifications for speed violations"}
                  </p>
                </div>
                <Switch />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{language === "ar" ? "التقارير اليومية" : "Daily Reports"}</Label>
                  <p className="text-sm text-muted-foreground">
                    {language === "ar" ? "تقارير يومية عن أداء الأسطول" : "Daily fleet performance reports"}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                {language === "ar" ? "إعدادات الأمان" : "Security Settings"}
              </CardTitle>
              <CardDescription>
                {language === "ar" ? "إدارة أمان الحساب والنظام" : "Manage account and system security"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="current-password">
                  {language === "ar" ? "كلمة المرور الحالية" : "Current Password"}
                </Label>
                <Input id="current-password" type="password" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-password">{language === "ar" ? "كلمة المرور الجديدة" : "New Password"}</Label>
                <Input id="new-password" type="password" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirm-password">{language === "ar" ? "تأكيد كلمة المرور" : "Confirm Password"}</Label>
                <Input id="confirm-password" type="password" />
              </div>
              <Button>{language === "ar" ? "تحديث كلمة المرور" : "Update Password"}</Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                {language === "ar" ? "إدارة البيانات" : "Data Management"}
              </CardTitle>
              <CardDescription>
                {language === "ar" ? "إعدادات النسخ الاحتياطي والتصدير" : "Backup and export settings"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{language === "ar" ? "النسخ الاحتياطي التلقائي" : "Automatic Backup"}</Label>
                  <p className="text-sm text-muted-foreground">
                    {language === "ar" ? "نسخ احتياطي يومي للبيانات" : "Daily automatic data backup"}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="space-y-2">
                <Label>{language === "ar" ? "تصدير البيانات" : "Export Data"}</Label>
                <div className="flex gap-2">
                  <Button variant="outline">{language === "ar" ? "تصدير CSV" : "Export CSV"}</Button>
                  <Button variant="outline">{language === "ar" ? "تصدير Excel" : "Export Excel"}</Button>
                  <Button variant="outline">{language === "ar" ? "تصدير JSON" : "Export JSON"}</Button>
                </div>
              </div>
              <div className="space-y-2">
                <Label>{language === "ar" ? "مسح البيانات" : "Data Cleanup"}</Label>
                <p className="text-sm text-muted-foreground">
                  {language === "ar"
                    ? "حذف البيانات القديمة والسجلات المنتهية الصلاحية"
                    : "Remove old data and expired records"}
                </p>
                <Button variant="destructive">{language === "ar" ? "تنظيف البيانات" : "Cleanup Data"}</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end">
        <Button>{language === "ar" ? "حفظ الإعدادات" : "Save Settings"}</Button>
      </div>
    </div>
  )
}
