"use client"

import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { LoadingSpinner } from '@/components/loading-spinner'

export default function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Don't redirect if user is already on login page
    if (!isLoading && !user && pathname !== '/login') {
      // Store the attempted URL to redirect back after login
      router.push(`/login?redirectedFrom=${encodeURIComponent(pathname)}`)
    }
  }, [user, isLoading, router, pathname])

  // Show loading spinner while checking auth status
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  // If user is authenticated, render the protected content
  if (user) {
    return <>{children}</>
  }

  // Don't render anything while redirecting
  return null
}