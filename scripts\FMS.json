{"tables": [{"name": "alerts", "columns": ["id", "vehicle_id", "driver_id", "alert_type", "alert_type_ar", "severity", "message", "message_ar", "acknowledged", "acknowledged_by", "acknowledged_at", "resolved", "resolved_by", "resolved_at", "metadata", "created_at"]}, {"name": "audit_logs", "columns": ["id", "table_name", "record_id", "action", "old_data", "new_data", "changed_by", "changed_at"]}, {"name": "branches", "columns": ["id", "brand_id", "name", "location", "manager_name", "contact_phone", "created_at"]}, {"name": "brands", "columns": ["id", "name", "description", "logo_url", "created_at"]}, {"name": "dashboard_configurations", "columns": ["id", "level_type", "level_id", "config_name", "config_value", "is_active", "created_at", "updated_at"]}, {"name": "dashboard_widgets", "columns": ["id", "level_type", "level_id", "widget_type", "widget_config", "display_order", "is_active", "created_at", "updated_at"]}, {"name": "drivers", "columns": ["id", "full_name", "national_id", "license_number", "license_expiry", "phone", "performance_score", "branch_id", "created_at"]}, {"name": "expenses", "columns": ["id", "vehicle_id", "expense_type", "amount_egp", "expense_date", "notes", "created_at"]}, {"name": "fuel_logs", "columns": ["id", "vehicle_id", "driver_id", "fuel_date", "liters", "cost_egp", "fuel_station", "km_at_refill", "created_at"]}, {"name": "locations", "columns": ["id", "vehicle_id", "timestamp", "lat", "lng", "speed_kph", "heading_degrees", "source", "created_at"]}, {"name": "maintenance", "columns": ["id", "vehicle_id", "service_date", "service_type", "description", "current_km", "severity", "vendor_id", "labor_cost_egp", "parts_cost_egp", "total_cost_egp", "notes", "created_at"]}, {"name": "maintenance_rules", "columns": ["id", "code", "vehicle_type", "fuel_type", "check_type", "service_type", "value", "created_at"]}, {"name": "reports", "columns": ["id", "name", "description", "report_type", "last_generated", "created_at"]}, {"name": "tires", "columns": ["id", "vehicle_id", "tire_brand", "tire_model", "tire_size", "tire_type", "position", "purchase_date", "installation_date", "installation_km", "current_km", "max_km", "tread_depth_mm", "min_tread_depth_mm", "pressure_psi", "recommended_pressure_psi", "status", "condition", "purchase_price", "vendor_id", "warranty_months", "notes", "created_by", "created_at", "updated_at"]}, {"name": "trips", "columns": ["id", "vehicle_id", "driver_id", "trip_number", "start_location", "end_location", "start_time", "end_time", "start_km", "end_km", "distance_km", "status", "purpose", "notes", "fuel_consumed_liters", "created_by", "created_at", "updated_at"]}, {"name": "users", "columns": ["id", "full_name", "email", "password_hash", "role", "job_title", "branch_id", "user_status", "last_login", "created_at", "updated_at"]}, {"name": "vehicle_assignments", "columns": ["id", "vehicle_id", "driver_id", "start_date", "end_date", "assignment_notes", "assigned_by", "created_at"]}, {"name": "vehicle_documents", "columns": ["id", "vehicle_id", "document_type", "document_type_ar", "document_number", "issue_date", "expiry_date", "issuing_authority", "issuing_authority_ar", "file_url", "notes", "status", "reminder_days", "created_by", "created_at", "updated_at"]}, {"name": "vehicles", "columns": ["id", "plate_number", "color", "vehicle_type", "service_type", "fuel_type", "year", "vin", "current_km", "last_maintenance_km", "next_maintenance_km", "next_maintenance_date", "last_tire_change_km", "next_tire_change_km", "next_tire_change_date", "insurance_expiry", "license_expiry", "status", "branch_id", "created_at", "license_type", "vehicle_features", "permits", "license_front_image", "license_back_image"]}, {"name": "upcoming_services", "columns": ["vehicle_id", "plate_number", "service_type", "service_type_ar", "trigger_value", "current_value", "remaining_km", "due_date", "reminder_days", "branch_id"]}, {"name": "vendors", "columns": ["id", "name", "contact_info", "service_type", "created_at"]}]}