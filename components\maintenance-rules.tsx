"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Edit, Trash2, Wrench } from "lucide-react"
import { LoadingSpinner } from "@/components/loading-spinner"
import { ErrorDisplay } from "@/components/error-display"
import { useAuthenticatedOperation } from "@/hooks/use-authenticated-client"
import { useToast } from "@/hooks/use-toast"
import { MaintenanceRuleDialog } from "@/components/forms/maintenance-rule-dialog"
import { useAuth } from "@/contexts/auth-context"

interface MaintenanceRule {
  id: string
  code: string
  vehicle_type: string | null
  fuel_type: string | null
  check_type: string
  service_type: string
  value: number
  created_at: string
}

interface MaintenanceRulesProps {
  language: "ar" | "en"
}

export function MaintenanceRules({ language }: MaintenanceRulesProps) {
  const [rules, setRules] = useState<MaintenanceRule[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [hasInitialized, setHasInitialized] = useState(false)
  const { executeOperation, client } = useAuthenticatedOperation()
  const { toast } = useToast()
  const { isLoading: authLoading, user } = useAuth()

  // State for controlling the add rule dialog
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  // State for controlling edit rule dialogs
  const [editingRule, setEditingRule] = useState<MaintenanceRule | null>(null)

  const fetchRules = useCallback(async () => {
    // Skip if already initialized
    if (hasInitialized) return

    // Skip if auth is still loading or user not available
    if (authLoading || !user || !client) return

    // Skip if already loading
    if (loading) return

    try {
      setLoading(true)
      setError(null)

      const { data, error } = await client
        .from("maintenance_rules")
        .select("*")
        .order("created_at", { ascending: false })

      if (error) {
        console.error("Database error:", error)
        throw new Error(`Failed to fetch maintenance rules: ${error.message}`)
      }

      const rules = Array.isArray(data) ? data : []
      setRules(rules)
      setHasInitialized(true)
      console.log(`Maintenance Rules: Loaded ${rules.length} rules`)
    } catch (err) {
      console.error("Error fetching maintenance rules:", err)
      setError(err instanceof Error ? err.message : "Failed to load maintenance rules")
    } finally {
      setLoading(false)
    }
  }, [hasInitialized, authLoading, user?.id, client, loading])

  useEffect(() => {
    fetchRules()
  }, [fetchRules])

  const handleDelete = async (id: string) => {
    try {
      // Check if we can perform the operation
      if (!client || authLoading || !user) {
        toast({
          title: language === "ar" ? "خطأ" : "Error",
          description: language === "ar" ? "النظام لا يزال يجهز الاتصال بقاعدة البيانات" : "System is still preparing database connection",
          variant: "destructive",
        })
        return
      }
      
      await executeOperation(async (client) => {
        const { error } = await client
          .from("maintenance_rules")
          .delete()
          .eq("id", id)

        if (error) throw error
      })

      toast({
        title: language === "ar" ? "تم حذف القاعدة" : "Rule Deleted",
        description: language === "ar" ? "تم حذف قاعدة الصيانة بنجاح" : "Maintenance rule has been deleted successfully",
      })

      // Refresh the list
      fetchRules()
    } catch (err) {
      console.error("Error deleting maintenance rule:", err)
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description: language === "ar" ? "حدث خطأ أثناء حذف القاعدة" : "An error occurred while deleting the rule",
        variant: "destructive",
      })
    }
  }

  // Show loading spinner while auth is loading or user is not authenticated
  if (authLoading || !user || loading) {
    return <LoadingSpinner className="h-64" />
  }

  if (error) {
    return <ErrorDisplay error={error} />
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            {language === "ar" ? "قواعد الصيانة" : "Maintenance Rules"}
          </h2>
          <p className="text-muted-foreground">
            {language === "ar" ? "إدارة قواعد وجدولة صيانة المركبات" : "Manage vehicle maintenance rules and scheduling"}
          </p>
        </div>
        <MaintenanceRuleDialog 
          language={language} 
          onRuleAdded={fetchRules} 
          open={isAddDialogOpen}
          onOpenChange={setIsAddDialogOpen}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            {language === "ar" ? "قواعد الصيانة" : "Maintenance Rules"}
          </CardTitle>
          <CardDescription>
            {language === "ar" ? "القواعد المحددة لجدولة الصيانة" : "Defined rules for maintenance scheduling"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === "ar" ? "الرمز" : "Code"}</TableHead>
                <TableHead>{language === "ar" ? "نوع المركبة" : "Vehicle Type"}</TableHead>
                <TableHead>{language === "ar" ? "نوع الوقود" : "Fuel Type"}</TableHead>
                <TableHead>{language === "ar" ? "نوع الفحص" : "Check Type"}</TableHead>
                <TableHead>{language === "ar" ? "نوع الخدمة" : "Service Type"}</TableHead>
                <TableHead>{language === "ar" ? "القيمة" : "Value"}</TableHead>
                <TableHead>{language === "ar" ? "تاريخ الإنشاء" : "Created At"}</TableHead>
                <TableHead>{language === "ar" ? "الإجراءات" : "Actions"}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {rules.map((rule) => (
                <TableRow key={rule.id}>
                  <TableCell className="font-medium">{rule.code}</TableCell>
                  <TableCell>{rule.vehicle_type || "-"}</TableCell>
                  <TableCell>{rule.fuel_type || "-"}</TableCell>
                  <TableCell>{rule.check_type}</TableCell>
                  <TableCell>{rule.service_type}</TableCell>
                  <TableCell>{rule.value}</TableCell>
                  <TableCell>
                    {new Date(rule.created_at).toLocaleDateString(language === "ar" ? "ar-EG" : "en-EG")}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => setEditingRule(rule)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleDelete(rule.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      {/* Edit dialog - only rendered when editingRule is not null */}
      {editingRule && (
        <MaintenanceRuleDialog 
          language={language} 
          rule={editingRule}
          onRuleAdded={() => {
            fetchRules()
            setEditingRule(null)
          }}
          open={!!editingRule}
          onOpenChange={(open) => {
            if (!open) {
              setEditingRule(null)
            }
          }}
        />
      )}
    </div>
  )
}