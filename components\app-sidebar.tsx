"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  LayoutDashboard,
  Car,
  Users,
  Wrench,
  Fuel,
  MapPin,
  AlertTriangle,
  DollarSign,
  FileText,
  Settings,
  ChevronUp,
  User2,
  LogOut,
} from "lucide-react"
import { useLanguage } from "@/contexts/language-context"
import { useCurrentUserProfile } from "@/hooks/use-fleet-data"
import { useAuth } from "@/contexts/auth-context"
import { cnWithFont } from "@/lib/utils"

export function AppSidebar() {
  const pathname = usePathname()
  const { language, isRTL } = useLanguage()
  const { data: currentUser, loading: userLoading } = useCurrentUserProfile()
  const { signOut } = useAuth()
  const [isLogoutDialogOpen, setIsLogoutDialogOpen] = useState(false)

  // Helper functions to get user display data
  const getUserDisplayName = () => {
    if (userLoading) return language === "ar" ? "جاري التحميل..." : "Loading..."
    if (currentUser?.full_name) return currentUser.full_name
    return language === "ar" ? "أحمد حسن" : "Ahmed Hassan" // Fallback
  }

  const getUserJobTitle = () => {
    if (userLoading) return language === "ar" ? "..." : "..."
    if (currentUser?.job_title) return currentUser.job_title
    // Fallback based on role
    if (currentUser?.role) {
      const roleLabels = {
        system_super_admin: language === "ar" ? "مدير النظام الأعلى" : "System Super Admin",
        admin: language === "ar" ? "مدير النظام" : "System Admin",
        fleet_manager: language === "ar" ? "مدير الأسطول" : "Fleet Manager",
        branch_manager: language === "ar" ? "مدير الفرع" : "Branch Manager",
        driver: language === "ar" ? "سائق" : "Driver",
        mechanic: language === "ar" ? "ميكانيكي" : "Mechanic",
        operator: language === "ar" ? "مشغل" : "Operator",
        viewer: language === "ar" ? "مشاهد" : "Viewer"
      }
      return roleLabels[currentUser.role as keyof typeof roleLabels] || currentUser.role
    }
    return language === "ar" ? "مدير النظام" : "System Admin" // Fallback
  }

  const getUserEmail = () => {
    if (currentUser?.email) return currentUser.email
    return "<EMAIL>" // Fallback
  }

  const getUserInitials = () => {
    const name = getUserDisplayName()
    if (name === "Loading..." || name === "جاري التحميل...") return "..."
    const nameParts = name.split(' ')
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase()
    }
    return name.substring(0, 2).toUpperCase()
  }

  const menuItems = [
    {
      href: "/",
      title: language === "ar" ? "لوحة التحكم" : "Dashboard",
      icon: LayoutDashboard,
    },
    {
      href: "/vehicles",
      title: language === "ar" ? "المركبات" : "Vehicles",
      icon: Car,
    },
    {
      href: "/driver",
      title: language === "ar" ? "السائقين" : "Driver",
      icon: Users,
    },
    {
      href: "/users",
      title: language === "ar" ? "المستخدمين" : "Users",
      icon: User2,
    },
    {
      href: "/maintenance",
      title: language === "ar" ? "الصيانة" : "Maintenance",
      icon: Wrench,
    },
    {
      href: "/maintenance_rules",
      title: language === "ar" ? "قواعد الصيانة" : "Maintenance Rules",
      icon: Wrench,
    },
    {
      href: "/fuel",
      title: language === "ar" ? "الوقود" : "Fuel",
      icon: Fuel,
    },
    {
      href: "/gps",
      title: language === "ar" ? "تتبع GPS" : "GPS Tracking",
      icon: MapPin,
    },
    {
      href: "/alerts",
      title: language === "ar" ? "التنبيهات" : "Alerts",
      icon: AlertTriangle,
    },
    {
      href: "/financial",
      title: language === "ar" ? "التقارير المالية" : "Financial Reports",
      icon: DollarSign,
    },
    {
      href: "/reports",
      title: language === "ar" ? "التقارير" : "Reports",
      icon: FileText,
    },
    {
      href: "/settings",
      title: language === "ar" ? "الإعدادات" : "Settings",
      icon: Settings,
    },
  ]

  return (
    <Sidebar collapsible="icon" side={isRTL ? "right" : "left"}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <Link href="/" legacyBehavior>
              <SidebarMenuButton
                size="lg"
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <Car className="size-4" />
                </div>
                <div className={`grid flex-1 text-sm leading-tight ${isRTL ? 'text-right' : 'text-left'}`}>
                  <span className={cnWithFont(language, "truncate font-semibold")}>
                    {language === "ar" ? "نظام إدارة الأسطول" : "Fleet Management System"}
                  </span>
                  <span className={cnWithFont(language, "truncate text-xs")}>
                    {language === "ar" ? "شركة النقل المتقدم" : "Advanced Transport Co."}
                  </span>
                </div>
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className={cnWithFont(language)}>{language === "ar" ? "القائمة الرئيسية" : "Main Menu"}</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.href}>
                  <Link href={item.href} legacyBehavior>
                    <SidebarMenuButton
                      tooltip={item.title}
                      isActive={pathname === item.href}
                    >
                      <item.icon />
                      <span className={cnWithFont(language)}>{item.title}</span>
                    </SidebarMenuButton>
                  </Link>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarFallback className="rounded-lg">{getUserInitials()}</AvatarFallback>
                  </Avatar>
                  <div className={`grid flex-1 text-sm leading-tight ${isRTL ? 'text-right' : 'text-left'}`}>
                    <span className={cnWithFont(language, "truncate font-semibold")}>{getUserDisplayName()}</span>
                    <span className={cnWithFont(language, "truncate text-xs")}>{getUserJobTitle()}</span>
                  </div>
                  <ChevronUp className={`size-4 ${isRTL ? 'mr-auto' : 'ml-auto'}`} />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarFallback className="rounded-lg">{getUserInitials()}</AvatarFallback>
                    </Avatar>
                    <div className={`grid flex-1 text-sm leading-tight ${isRTL ? 'text-right' : 'text-left'}`}>
                      <span className={cnWithFont(language, "truncate font-semibold")}>{getUserDisplayName()}</span>
                      <span className={cnWithFont(language, "truncate text-xs")}>{getUserEmail()}</span>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/settings">
                    <User2 />
                    <span className={cnWithFont(language)}>
                      {language === "ar" ? "الملف الشخصي" : "Profile"}
                    </span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/settings">
                    <Settings />
                    <span className={cnWithFont(language)}>
                      {language === "ar" ? "الإعدادات" : "Settings"}
                    </span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <AlertDialog open={isLogoutDialogOpen} onOpenChange={setIsLogoutDialogOpen}>
                  <AlertDialogTrigger asChild>
                    <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                      <LogOut />
                      <span className={cnWithFont(language)}>
                        {language === "ar" ? "تسجيل الخروج" : "Log out"}
                      </span>
                    </DropdownMenuItem>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle className={cnWithFont(language)}>
                        {language === "ar" ? "تأكيد تسجيل الخروج" : "Confirm Logout"}
                      </AlertDialogTitle>
                      <AlertDialogDescription className={cnWithFont(language)}>
                        {language === "ar" 
                          ? "هل أنت متأكد من أنك تريد تسجيل الخروج؟ سيتم إنهاء جلستك الحالية وستحتاج إلى تسجيل الدخول مرة أخرى."
                          : "Are you sure you want to log out? This will end your current session and you'll need to sign in again."}
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel className={cnWithFont(language)}>
                        {language === "ar" ? "إلغاء" : "Cancel"}
                      </AlertDialogCancel>
                      <AlertDialogAction
                        onClick={async () => {
                          try {
                            await signOut()
                          } catch (error) {
                            console.error('Logout error:', error)
                          }
                        }}
                        className={cnWithFont(language, "bg-red-600 hover:bg-red-700 focus:ring-red-600")}
                      >
                        {language === "ar" ? "تسجيل الخروج" : "Log out"}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>

      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}