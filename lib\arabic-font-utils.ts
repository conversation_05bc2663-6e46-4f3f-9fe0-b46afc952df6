/**
 * Arabic Font Utilities for PDF Generation
 * Provides proper Arabic text handling and font support for jsPDF
 */

import jsPDF from 'jspdf'

/**
 * Ensures proper Arabic font handling in PDFs
 * @param pdf The jsPDF instance
 * @param isArabic Whether the content is in Arabic
 */
export async function ensureArabicFont(pdf: jsPDF): Promise<void> {
  try {
    // Check if we're in Arabic mode
    const isArabicMode = document.documentElement.lang === 'ar' || 
                         document.body.classList.contains('rtl') ||
                         document.body.dataset.language === 'ar'
    
    if (isArabicMode) {
      // The Cairo font is already loaded via Google Fonts in the layout
      // We just need to ensure it's available for PDF generation
      console.log('Arabic mode detected, Cairo font will be used for PDF generation')
    }
  } catch (error) {
    console.warn('Error ensuring Arabic font availability:', error)
  }
}

// Enhanced PDF support with proper Arabic text handling
export function createArabicPDFSupport(pdf: jsPDF, isArabic: boolean) {
  // Set default font to Cairo for Arabic content
  if (isArabic) {
    // Use Cairo font which is loaded via Google Fonts
    // Note: jsPDF doesn't directly support web fonts, so we'll use built-in fonts
    // but ensure proper text direction and handling
    pdf.setFont('helvetica')
  }

  return {
    // Add text with proper Arabic support
    addText: (text: string, x: number, y: number, options: any = {}) => {
      const {
        font = 'helvetica',
        style = 'normal',
        fontSize = 12,
        align = 'left'
      } = options

      pdf.setFont(font, style)
      pdf.setFontSize(fontSize)

      if (isArabic) {
        // For Arabic text, we need to handle right-to-left properly
        // This is a simplified approach - in practice, you might need more sophisticated handling
        const processedText = text
        const textWidth = pdf.getTextWidth(processedText)
        
        // Adjust x position for right alignment in RTL
        let adjustedX = x
        if (align === 'right') {
          adjustedX = x - textWidth
        } else if (align === 'center') {
          adjustedX = x - (textWidth / 2)
        }

        pdf.text(processedText, adjustedX, y)
      } else {
        pdf.text(text, x, y, { align: align as any })
      }
    },

    // Async version for handling font loading
    addTextAsync: async (text: string, x: number, y: number, options: any = {}) => {
      return new Promise<void>((resolve) => {
        try {
          const {
            font = 'helvetica',
            style = 'normal',
            fontSize = 12,
            align = 'left'
          } = options

          pdf.setFont(font, style)
          pdf.setFontSize(fontSize)

          if (isArabic) {
            // For Arabic text, ensure proper RTL handling
            const processedText = text
            const textWidth = pdf.getTextWidth(processedText)
            
            // Adjust x position for right alignment in RTL
            let adjustedX = x
            if (align === 'right') {
              adjustedX = x - textWidth
            } else if (align === 'center') {
              adjustedX = x - (textWidth / 2)
            }

            pdf.text(processedText, adjustedX, y)
          } else {
            pdf.text(text, x, y, { align: align as any })
          }
          resolve()
        } catch (error) {
          console.warn('Error adding text to PDF:', error)
          // Fallback to basic text addition
          pdf.text(text, x, y)
          resolve()
        }
      })
    },

    // Add section header with proper styling
    addSectionHeader: (title: string, x: number, y: number, width: number, color: [number, number, number]) => {
      const headerHeight = 7
      pdf.setFillColor(...color)
      pdf.rect(x, y, width, headerHeight, 'F')
      
      pdf.setTextColor(255, 255, 255)
      pdf.setFontSize(10)
      pdf.setFont('helvetica', 'bold')
      
      const textX = isArabic ? x + width - 4 : x + 4
      pdf.text(title, textX, y + 4.5, { align: isArabic ? 'right' : 'left' } as any)
      
      pdf.setTextColor(55, 65, 81) // Reset to default text color
      pdf.setFont('helvetica', 'normal')
      pdf.setFontSize(10)
      
      return y + headerHeight + 2
    },

    // Add data row with proper formatting
    addDataRow: (label: string, value: string, x: number, y: number, width: number, isEven: boolean) => {
      if (isEven) {
        pdf.setFillColor(249, 250, 251)
        pdf.rect(x, y - 1.75, width, 6, 'F')
      }
      
      pdf.setFontSize(9)
      
      if (isArabic) {
        // Arabic layout - label on right, value on left
        pdf.text(label, x + width - 4, y + 3, { align: 'right' } as any)
        pdf.text(value, x + 4, y + 3, { align: 'left' } as any)
      } else {
        // English layout - label on left, value on right
        pdf.text(label, x + 4, y + 3, { align: 'left' } as any)
        pdf.text(value, x + width - 4, y + 3, { align: 'right' } as any)
      }
      
      return y + 6
    },

    // Add alert box with proper styling
    addAlertBox: (text: string, x: number, y: number, width: number, color: [number, number, number]) => {
      const alertHeight = 8
      pdf.setFillColor(...color)
      pdf.rect(x, y, width, alertHeight, 'F')
      
      pdf.setTextColor(255, 255, 255)
      pdf.setFontSize(8)
      
      const textX = isArabic ? x + width - 4 : x + 4
      pdf.text(text, textX, y + 5, { align: isArabic ? 'right' : 'left' } as any)
      
      pdf.setTextColor(55, 65, 81) // Reset to default text color
      
      return y + alertHeight + 2
    }
  }
}

// Utility function to convert Arabic text for better PDF rendering
export function processArabicText(text: string): string {
  // This is a placeholder function - in a real implementation, you might need
  // more sophisticated Arabic text processing for proper rendering in PDFs
  return text
}

// Font loading utility
export async function loadCairoFont(): Promise<void> {
  return new Promise((resolve) => {
    // In a browser environment, the Cairo font should already be loaded via Google Fonts
    // This function is a placeholder for any additional font loading logic if needed
    console.log('Cairo font loading check completed')
    resolve()
  })
}

export default {
  ensureArabicFont,
  createArabicPDFSupport,
  processArabicText,
  loadCairoFont
}