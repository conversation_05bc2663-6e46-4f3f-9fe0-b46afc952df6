import { VehicleUpdateDiagnostic } from "@/components/vehicle-update-diagnostic"
import { DatabaseSchemaDiagnostic } from "@/components/database-schema-diagnostic"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function DiagnosticPage() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">Fleet Management Diagnostics</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Use these diagnostic tools to troubleshoot database issues, verify schema integrity, 
          and test vehicle update operations.
        </p>
      </div>

      <Alert>
        <AlertDescription>
          <strong>Important:</strong> These diagnostic tools are designed to help identify and resolve 
          database schema mismatches and vehicle update issues. Use them when experiencing errors 
          with vehicle operations.
        </AlertDescription>
      </Alert>

      <div className="space-y-8">
        {/* Database Schema Diagnostic */}
        <Card>
          <CardHeader>
            <CardTitle>Database Schema Diagnostic</CardTitle>
            <CardDescription>
              Check actual database table structure vs expected schema and generate migration SQL
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DatabaseSchemaDiagnostic />
          </CardContent>
        </Card>

        {/* Vehicle Update Diagnostic */}
        <Card>
          <CardHeader>
            <CardTitle>Vehicle Update Diagnostic</CardTitle>
            <CardDescription>
              Test vehicle update operations and verify authentication & field mapping
            </CardDescription>
          </CardHeader>
          <CardContent>
            <VehicleUpdateDiagnostic />
          </CardContent>
        </Card>
      </div>

      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-medium text-blue-900 mb-2">How to Fix Schema Issues:</h3>
        <ol className="list-decimal list-inside space-y-2 text-blue-800 text-sm">
          <li>Run the Database Schema Diagnostic to identify missing columns</li>
          <li>Copy the generated migration SQL from the diagnostic results</li>
          <li>Go to your Supabase dashboard → SQL Editor</li>
          <li>Run the migration SQL to add missing columns</li>
          <li>Re-run the Vehicle Update Diagnostic to verify the fix</li>
          <li>Test vehicle update operations in the fleet management interface</li>
        </ol>
      </div>
    </div>
  )
}