"use client"

import { useState } from "react"

import { CardDescription } from "@/components/ui/card"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { FileText, Download, CalendarIcon, Eye, Trash2 } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface ReportsProps {
  language: "ar" | "en"
}

// Mock reports data
const savedReports = [
  {
    id: "1",
    name: "Monthly Fleet Performance",
    type: "performance",
    created_date: "2024-01-15",
    created_by: "<PERSON>",
    format: "PDF",
    size: "2.3 MB",
    status: "completed",
  },
  {
    id: "2",
    name: "Fuel Consumption Analysis",
    type: "fuel",
    created_date: "2024-01-14",
    created_by: "Mohamed Ali",
    format: "Excel",
    size: "1.8 MB",
    status: "completed",
  },
  {
    id: "3",
    name: "Driver Performance Report",
    type: "driver",
    created_date: "2024-01-13",
    created_by: "Hassan Ahmed",
    format: "PDF",
    size: "3.1 MB",
    status: "completed",
  },
  {
    id: "4",
    name: "Maintenance Cost Summary",
    type: "maintenance",
    created_date: "2024-01-12",
    created_by: "Omar Mahmoud",
    format: "Excel",
    size: "2.7 MB",
    status: "processing",
  },
]

const reportTemplates = (language: "ar" | "en") => [
  {
    id: "fleet_performance",
    name: language === "ar" ? "تقرير أداء الأسطول" : "Fleet Performance Report",
    description:
      language === "ar"
        ? "تحليل شامل لأداء المركبات والسائقين"
        : "Comprehensive analysis of vehicle and driver performance",
    category: "performance",
  },
  {
    id: "fuel_analysis",
    name: language === "ar" ? "تحليل استهلاك الوقود" : "Fuel Consumption Analysis",
    description:
      language === "ar" ? "تقرير مفصل عن استهلاك الوقود والتكاليف" : "Detailed report on fuel consumption and costs",
    category: "fuel",
  },
  {
    id: "maintenance_summary",
    name: language === "ar" ? "ملخص الصيانة" : "Maintenance Summary",
    description: language === "ar" ? "تقرير عن أعمال الصيانة والتكاليف" : "Report on maintenance activities and costs",
    category: "maintenance",
  },
  {
    id: "driver_performance",
    name: language === "ar" ? "تقرير أداء السائقين" : "Driver Performance Report",
    description: language === "ar" ? "تقييم أداء السائقين والمخالفات" : "Driver performance evaluation and violations",
    category: "driver",
  },
  {
    id: "financial_summary",
    name: language === "ar" ? "الملخص المالي" : "Financial Summary",
    description: language === "ar" ? "تقرير شامل عن التكاليف والمصروفات" : "Comprehensive cost and expense report",
    category: "financial",
  },
]

export function Reports({ language }: ReportsProps) {
  const [activeTab, setActiveTab] = useState("generate")
  const [selectedTemplate, setSelectedTemplate] = useState("")
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })
  const [selectedVehicles, setSelectedVehicles] = useState<string[]>([])
  const [selectedDrivers, setSelectedDrivers] = useState<string[]>([])
  const [reportFormat, setReportFormat] = useState("pdf")
  const [isGenerateDialogOpen, setIsGenerateDialogOpen] = useState(false)

  const getStatusBadge = (status: string) => {
    const statusMap = {
      completed: { variant: "default" as const, label: language === "ar" ? "مكتمل" : "Completed" },
      processing: { variant: "outline" as const, label: language === "ar" ? "قيد المعالجة" : "Processing" },
      failed: { variant: "destructive" as const, label: language === "ar" ? "فشل" : "Failed" },
    }
    const statusInfo = statusMap[status as keyof typeof statusMap] || { variant: "secondary" as const, label: status }
    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          statusInfo.variant === "default"
            ? "bg-green-100 text-green-800"
            : statusInfo.variant === "outline"
              ? "bg-yellow-100 text-yellow-800"
              : statusInfo.variant === "destructive"
                ? "bg-red-100 text-red-800"
                : "bg-gray-100 text-gray-800"
        }`}
      >
        {statusInfo.label}
      </span>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">{language === "ar" ? "التقارير" : "Reports"}</h2>
        <p className="text-muted-foreground">
          {language === "ar" ? "تقارير شاملة عن الأسطول" : "Comprehensive fleet reports"}
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
        <Button
          variant={activeTab === "generate" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveTab("generate")}
        >
          <FileText className="h-4 w-4 mr-2" />
          {language === "ar" ? "إنشاء تقرير" : "Generate Report"}
        </Button>
        <Button variant={activeTab === "saved" ? "default" : "ghost"} size="sm" onClick={() => setActiveTab("saved")}>
          <Download className="h-4 w-4 mr-2" />
          {language === "ar" ? "التقارير المحفوظة" : "Saved Reports"}
        </Button>
      </div>

      {activeTab === "generate" && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {reportTemplates(language).map((template) => (
            <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  {template.name}
                </CardTitle>
                <CardDescription>{template.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <Dialog open={isGenerateDialogOpen} onOpenChange={setIsGenerateDialogOpen}>
                  <DialogTrigger asChild>
                    <Button className="w-full" onClick={() => setSelectedTemplate(template.id)}>
                      {language === "ar" ? "إنشاء التقرير" : "Generate Report"}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                      <DialogTitle>{language === "ar" ? "إعدادات التقرير" : "Report Settings"}</DialogTitle>
                      <DialogDescription>
                        {language === "ar"
                          ? "اختر المعايير والإعدادات للتقرير"
                          : "Select criteria and settings for the report"}
                      </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="dateRange" className="text-right">
                          {language === "ar" ? "الفترة الزمنية" : "Date Range"}
                        </Label>
                        <div className="col-span-3 flex gap-2">
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn(
                                  "justify-start text-left font-normal flex-1",
                                  !dateRange.from && "text-muted-foreground",
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {dateRange.from ? (
                                  format(dateRange.from, "PPP")
                                ) : (
                                  <span>{language === "ar" ? "من" : "From"}</span>
                                )}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar
                                mode="single"
                                selected={dateRange.from}
                                onSelect={(date) => setDateRange((prev) => ({ ...prev, from: date }))}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn(
                                  "justify-start text-left font-normal flex-1",
                                  !dateRange.to && "text-muted-foreground",
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {dateRange.to ? (
                                  format(dateRange.to, "PPP")
                                ) : (
                                  <span>{language === "ar" ? "إلى" : "To"}</span>
                                )}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar
                                mode="single"
                                selected={dateRange.to}
                                onSelect={(date) => setDateRange((prev) => ({ ...prev, to: date }))}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </div>
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="format" className="text-right">
                          {language === "ar" ? "تنسيق التقرير" : "Report Format"}
                        </Label>
                        <Select value={reportFormat} onValueChange={setReportFormat}>
                          <SelectTrigger className="col-span-3">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pdf">PDF</SelectItem>
                            <SelectItem value="excel">Excel</SelectItem>
                            <SelectItem value="csv">CSV</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="grid grid-cols-4 items-start gap-4">
                        <Label className="text-right">{language === "ar" ? "المركبات" : "Vehicles"}</Label>
                        <div className="col-span-3 space-y-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox id="all-vehicles" />
                            <Label htmlFor="all-vehicles">{language === "ar" ? "جميع المركبات" : "All Vehicles"}</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox id="vehicle-1" />
                            <Label htmlFor="vehicle-1">ABC-123 - Toyota Camry</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox id="vehicle-2" />
                            <Label htmlFor="vehicle-2">XYZ-789 - Honda Civic</Label>
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-4 items-start gap-4">
                        <Label className="text-right">{language === "ar" ? "السائقون" : "Drivers"}</Label>
                        <div className="col-span-3 space-y-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox id="all-drivers" />
                            <Label htmlFor="all-drivers">{language === "ar" ? "جميع السائقين" : "All Drivers"}</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox id="driver-1" />
                            <Label htmlFor="driver-1">Ahmed Hassan</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox id="driver-2" />
                            <Label htmlFor="driver-2">Mohamed Ali</Label>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setIsGenerateDialogOpen(false)}>
                        {language === "ar" ? "إلغاء" : "Cancel"}
                      </Button>
                      <Button onClick={() => setIsGenerateDialogOpen(false)}>
                        {language === "ar" ? "إنشاء التقرير" : "Generate Report"}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {activeTab === "saved" && (
        <Card>
          <CardHeader>
            <CardTitle>{language === "ar" ? "التقارير المحفوظة" : "Saved Reports"}</CardTitle>
            <CardDescription>
              {language === "ar" ? "التقارير التي تم إنشاؤها مسبقاً" : "Previously generated reports"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {savedReports.map((report) => (
                <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <FileText className="h-8 w-8 text-muted-foreground" />
                    <div>
                      <h3 className="font-medium">{report.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {language === "ar" ? "أنشئ بواسطة" : "Created by"} {report.created_by} •{" "}
                        {new Date(report.created_date).toLocaleDateString()} • {report.format} • {report.size}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(report.status)}
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
