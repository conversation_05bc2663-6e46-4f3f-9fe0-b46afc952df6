"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { AddFuelDialog } from "@/components/forms/add-fuel-dialog"
import { Fuel, TrendingUp, TrendingDown } from "lucide-react"
import { useFuelTrends } from "@/hooks/use-fleet-data"
import { LoadingSpinner } from "@/components/loading-spinner"
import { ErrorDisplay } from "@/components/error-display"

interface FuelManagementProps {
  language: "ar" | "en"
}

export function FuelManagement({ language }: FuelManagementProps) {
  const { data: fuelTrends, loading, error } = useFuelTrends(30)

  if (loading) {
    return <LoadingSpinner className="h-64" />
  }

  if (error) {
    return <ErrorDisplay error={error} />
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-EG", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const totalCost = fuelTrends.reduce((sum, day) => sum + day.total_cost_egp, 0)
  const totalLiters = fuelTrends.reduce((sum, day) => sum + day.total_liters, 0)
  const avgPricePerLiter = totalLiters > 0 ? totalCost / totalLiters : 0

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            {language === "ar" ? "إدارة الوقود" : "Fuel Management"}
          </h2>
          <p className="text-muted-foreground">
            {language === "ar" ? "تتبع وإدارة استهلاك الوقود" : "Track and manage fuel consumption"}
          </p>
        </div>
        <AddFuelDialog language={language} />
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "إجمالي التكلفة (30 يوم)" : "Total Cost (30 days)"}
            </CardTitle>
            <Fuel className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalCost)}</div>
            <p className="text-xs text-muted-foreground">
              {language === "ar" ? "تكلفة الوقود الإجمالية" : "Total fuel cost"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "إجمالي اللترات" : "Total Liters"}
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalLiters.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">{language === "ar" ? "لتر وقود" : "Liters of fuel"}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "متوسط السعر/لتر" : "Avg Price/Liter"}
            </CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {avgPricePerLiter.toFixed(2)} {language === "ar" ? "ج.م" : "EGP"}
            </div>
            <p className="text-xs text-muted-foreground">
              {language === "ar" ? "متوسط سعر اللتر" : "Average price per liter"}
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{language === "ar" ? "سجلات الوقود الحديثة" : "Recent Fuel Records"}</CardTitle>
          <CardDescription>
            {language === "ar" ? "آخر عمليات تزويد الوقود" : "Latest fuel refill entries"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">
            {language === "ar" ? "سيتم عرض سجلات الوقود هنا" : "Fuel records will be displayed here"}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
