"use client"

import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { DashboardHeader } from "@/components/dashboard-header"
import { LanguageProvider, useLanguage } from "@/contexts/language-context"
import ProtectedRoute from "@/components/protected-route"

function DashboardContent({ children }: { children: React.ReactNode }) {
  const { language, direction, isRTL } = useLanguage()

  // Language and direction are now automatically managed by the language context
  // No need to manually set attributes here

  return (
    <SidebarProvider>
      <div className={`flex min-h-screen w-full ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
        <AppSidebar />
        <SidebarInset className="flex-1">
          <DashboardHeader />
          <main className="flex-1 p-6">{children}</main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  )
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <LanguageProvider>
      <ProtectedRoute>
        <DashboardContent>
          {children}
        </DashboardContent>
      </ProtectedRoute>
    </LanguageProvider>
  )
}