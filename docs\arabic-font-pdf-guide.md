# Arabic Font PDF Generation Guide

This guide explains how the Cairo font is implemented for Arabic text in PDF outputs within the Fleet Management Dashboard application.

## Font Implementation

### 1. Cairo Font Loading

The Cairo font is loaded via Google Fonts in the main layout file:

```tsx
// app/layout.tsx
import { Cairo } from "next/font/google"
const cairo = Cairo({ subsets: ["arabic"], variable: "--font-cairo" })
```

The font is made available through CSS variables in the global stylesheet:

```css
/* app/globals.css */
.font-arabic, [lang="ar"] {
  font-family: var(--font-cairo), 'Cairo', "Segoe UI", -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
}
```

### 2. PDF Generation with Arabic Support

For PDF generation, we use jsPDF with custom utilities to ensure proper Arabic text handling:

```typescript
// lib/pdf-arabic-utils.ts
import jsPDF from 'jspdf'

export function createEnhancedPDFHandler(pdf: jsPDF, isArabic: boolean) {
  return {
    addText: (text: string, x: number, y: number, options: any = {}) => {
      // Implementation with proper RTL handling
    }
  }
}
```

### 3. Vehicle Profile PDF Example

The vehicle profile dialog demonstrates Arabic PDF generation:

```typescript
// components/forms/vehicle-profile-dialog.tsx
const generatePDF = async () => {
  const pdf = new jsPDF('p', 'mm', 'a4')
  const isArabic = language === "ar"
  
  // Setup Arabic PDF support
  await setupArabicPDFSupport(pdf, isArabic)
  applyCairoFontForArabic(pdf, isArabic)
  
  // Create enhanced PDF handler
  const pdfHandler = createEnhancedPDFHandler(pdf, isArabic)
}
```

## Key Features

### Font Priority
1. **Primary**: Cairo font (loaded via Google Fonts)
2. **Fallback**: Helvetica (built-in PDF font)
3. **RTL Support**: Right-to-left text direction for Arabic content

### Text Handling
- Proper right-to-left text alignment
- Correct positioning for Arabic characters
- Section headers with appropriate styling
- Data rows with bilingual label/value positioning

### PDF Structure
- Professional headers with branding
- Color-coded sections for different content types
- Responsive layout that works in both languages
- Proper footer with generation timestamp

## Usage Guidelines

### For Developers
1. Always import the PDF Arabic utilities when generating PDFs with Arabic content
2. Check the language context to determine if Arabic font should be applied
3. Use the enhanced PDF handler for consistent text rendering
4. Test PDF output in both Arabic and English modes

### For Content Creators
1. Arabic text will automatically use the Cairo font in PDFs
2. Text direction is handled automatically (RTL for Arabic)
3. Section headers and data formatting adapt to the language
4. Special characters and punctuation are properly positioned

## Troubleshooting

### Font Not Loading
- Ensure the Cairo font is properly imported in the layout
- Check that the Google Fonts URL is accessible
- Verify CSS variables are correctly applied

### Text Alignment Issues
- Use the enhanced PDF handler functions instead of direct jsPDF methods
- Ensure the `isArabic` flag is correctly set
- Check that RTL positioning is applied for Arabic content

### PDF Generation Errors
- Verify all text processing functions handle Arabic characters
- Ensure proper error handling in async PDF generation
- Test with various Arabic text samples

## Future Improvements

1. **Advanced Font Support**: Direct embedding of Cairo font in PDFs
2. **Enhanced RTL Handling**: More sophisticated Arabic text processing
3. **Dynamic Font Loading**: Runtime font loading for offline scenarios
4. **Extended Language Support**: Support for other RTL languages

## References

- [Cairo Font on Google Fonts](https://fonts.google.com/specimen/Cairo)
- [jsPDF Documentation](https://artskydj.github.io/jsPDF/docs/)
- [Next.js Font Optimization](https://nextjs.org/docs/pages/building-your-application/optimizing/fonts)