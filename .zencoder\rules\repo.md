---
description: Repository Information Overview
alwaysApply: true
---

# Fleetlytic Information

## Summary
Fleetlytic is a comprehensive fleet management dashboard built with Next.js, React, and Supabase. It provides monitoring, management, and analytics for vehicle fleets, enabling organizations to streamline operations across maintenance, fuel usage, GPS tracking, driver management, financial reporting, and user access control.

## Structure
- **app/**: Next.js application routes and pages
- **components/**: React components including UI elements and feature modules
- **contexts/**: React context providers for auth and language
- **hooks/**: Custom React hooks for data fetching and state management
- **lib/**: Utility functions and Supabase client configuration
- **public/**: Static assets
- **scripts/**: SQL scripts for database setup

## Language & Runtime
**Language**: TypeScript
**Version**: TypeScript 5.x
**Framework**: Next.js 15.2.4, React 19
**Package Manager**: pnpm

## Dependencies
**Main Dependencies**:
- Next.js 15.2.4 (React framework)
- React 19 (UI library)
- Supabase (Backend and authentication)
- Tailwind CSS (Styling)
- Radix <PERSON> (UI primitives)
- React Hook Form (Form handling)
- Zod (Validation)
- Recharts (Data visualization)

**Development Dependencies**:
- TypeScript 5.x
- PostCSS 8.5+
- Tailwind CSS 3.4.17

## Build & Installation
```bash
# Install dependencies
pnpm install

# Development server
pnpm dev

# Production build
pnpm run build

# Start production server
pnpm start
```

## Deployment
**Platform**: Vercel
**Configuration**: Static export using `output: 'export'` in next.config.mjs
**Environment Variables**:
- NEXT_PUBLIC_SUPABASE_URL
- NEXT_PUBLIC_SUPABASE_ANON_KEY
- NEXT_PUBLIC_GEMINI_API_KEY

**Deployment Command**:
```bash
vercel --prod
```

## Authentication
**Provider**: Supabase Auth
**Method**: Email/Password authentication
**User Roles**:
- system_super_admin
- admin
- fleet_manager
- branch_manager
- driver
- mechanic
- operator
- viewer

## Database Schema
**Provider**: Supabase
**Main Tables**:
- Brand
- Branch
- User
- Vehicle
- Driver
- VehicleAssignment
- Maintenance
- FuelLog
- Alert

**Views**:
- DashboardOverviewAll
- FleetOverview
- UpcomingService
- MaintenanceStatistics

## Features
- Dashboard with executive summary and KPIs
- Alerts & Diagnostics for real-time vehicle health
- Fleet & Vehicle Management
- Maintenance scheduling and tracking
- Fuel consumption monitoring
- GPS tracking and route visualization
- Driver and user management
- Financial reporting and analytics
- Role-based access control
- Exportable reports and data visualization