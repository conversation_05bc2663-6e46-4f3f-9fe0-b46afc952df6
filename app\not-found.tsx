"use client"

import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/contexts/language-context'

// Force dynamic rendering to avoid static generation issues
export const dynamic = "force-dynamic"

export default function NotFound() {
  const router = useRouter()
  const { t, language } = useLanguage()

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background p-4">
      <div className="text-center max-w-md">
        <h1 className="text-4xl font-bold mb-4">
          {language === 'ar' ? 'الصفحة غير موجودة' : 'Page Not Found'}
        </h1>
        <p className="text-lg mb-8 text-muted-foreground">
          {language === 'ar' 
            ? 'عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.' 
            : 'Sorry, the page you are looking for does not exist or has been moved.'}
        </p>
        <Button onClick={() => router.push('/')}>
          {language === 'ar' ? 'العودة إلى الرئيسية' : 'Go Home'}
        </Button>
      </div>
    </div>
  )
}