"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Eye, EyeOff } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"
import bcrypt from "bcryptjs"

interface AddUserDialogProps {
  language: "ar" | "en"
  onUserAdded?: () => void
}

interface Branch {
  id: string
  name: string
}

export function AddUserDialog({ language, onUserAdded }: AddUserDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [branches, setBranches] = useState<Branch[]>([])
  const { toast } = useToast()

  const [formData, setFormData] = useState({
    full_name: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: "viewer" as const,
    job_title: "",
    branch_id: "",
    user_status: "active" as const,
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Load branches on component mount
  useEffect(() => {
    async function loadBranches() {
      try {
        const { data, error } = await supabase
          .from("branches")
          .select("id, name")
          .order("name")

        if (error) throw error
        setBranches(data || [])
      } catch (err) {
        console.error("Error loading branches:", err)
      }
    }

    loadBranches()
  }, [])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Required fields
    if (!formData.full_name.trim()) {
      newErrors.full_name = language === "ar" ? "الاسم الكامل مطلوب" : "Full name is required"
    }

    if (!formData.email.trim()) {
      newErrors.email = language === "ar" ? "البريد الإلكتروني مطلوب" : "Email is required"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = language === "ar" ? "البريد الإلكتروني غير صحيح" : "Invalid email format"
    }

    if (!formData.password) {
      newErrors.password = language === "ar" ? "كلمة المرور مطلوبة" : "Password is required"
    } else if (formData.password.length < 8) {
      newErrors.password = language === "ar" ? "كلمة المرور يجب أن تكون 8 أحرف على الأقل" : "Password must be at least 8 characters"
    } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = language === "ar" 
        ? "كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم"
        : "Password must contain uppercase, lowercase, and number"
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = language === "ar" ? "كلمة المرور غير متطابقة" : "Passwords do not match"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      // Hash the password
      const saltRounds = 12
      const hashedPassword = await bcrypt.hash(formData.password, saltRounds)

      // Prepare data for insertion
      const userData = {
        full_name: formData.full_name.trim(),
        email: formData.email.toLowerCase().trim(),
        password_hash: hashedPassword,
        role: formData.role,
        job_title: formData.job_title.trim() || null,
        branch_id: formData.branch_id || null,
        user_status: formData.user_status,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      // Insert into Supabase
      const { data, error } = await supabase.from("users").insert([userData]).select()

      if (error) {
        // Handle specific error cases
        if (error.code === "23505" && error.message.includes("email")) {
          throw new Error(language === "ar" ? "البريد الإلكتروني مستخدم بالفعل" : "Email already exists")
        }
        throw error
      }

      // Show success message
      toast({
        title: language === "ar" ? "تم إضافة المستخدم" : "User Added",
        description:
          language === "ar"
            ? `تم إضافة المستخدم ${formData.full_name} بنجاح`
            : `User ${formData.full_name} has been added successfully`,
      })

      // Reset form and close dialog
      setFormData({
        full_name: "",
        email: "",
        password: "",
        confirmPassword: "",
        role: "viewer",
        job_title: "",
        branch_id: "",
        user_status: "active",
      })
      setErrors({})
      setOpen(false)

      // Trigger refresh if callback provided
      if (onUserAdded) {
        onUserAdded()
      }
    } catch (err) {
      console.error("Error adding user:", err)
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description: err instanceof Error ? err.message : (language === "ar" ? "حدث خطأ أثناء إضافة المستخدم" : "An error occurred while adding the user"),
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const roleOptions = [
    { value: "viewer", label: language === "ar" ? "مشاهد" : "Viewer" },
    { value: "operator", label: language === "ar" ? "مشغل" : "Operator" },
    { value: "mechanic", label: language === "ar" ? "ميكانيكي" : "Mechanic" },
    { value: "driver", label: language === "ar" ? "سائق" : "Driver" },
    { value: "branch_manager", label: language === "ar" ? "مدير فرع" : "Branch Manager" },
    { value: "fleet_manager", label: language === "ar" ? "مدير أسطول" : "Fleet Manager" },
    { value: "admin", label: language === "ar" ? "مدير" : "Admin" },
    { value: "system_super_admin", label: language === "ar" ? "مدير النظام الأعلى" : "System Super Admin" },
  ]

  const statusOptions = [
    { value: "active", label: language === "ar" ? "نشط" : "Active" },
    { value: "inactive", label: language === "ar" ? "غير نشط" : "Inactive" },
    { value: "suspended", label: language === "ar" ? "موقوف" : "Suspended" },
    { value: "pending", label: language === "ar" ? "في الانتظار" : "Pending" },
  ]

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          {language === "ar" ? "إضافة مستخدم" : "Add User"}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{language === "ar" ? "إضافة مستخدم جديد" : "Add New User"}</DialogTitle>
          <DialogDescription>
            {language === "ar" ? "أدخل تفاصيل المستخدم الجديد وصلاحياته" : "Enter the details and permissions for the new user"}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            {/* Full Name */}
            <div className="space-y-2">
              <Label htmlFor="full_name">{language === "ar" ? "الاسم الكامل" : "Full Name"} *</Label>
              <Input
                id="full_name"
                value={formData.full_name}
                onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                placeholder={language === "ar" ? "أحمد محمد علي" : "Ahmed Mohamed Ali"}
                className={errors.full_name ? "border-destructive" : ""}
              />
              {errors.full_name && <p className="text-sm text-destructive">{errors.full_name}</p>}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">{language === "ar" ? "البريد الإلكتروني" : "Email"} *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder={language === "ar" ? "<EMAIL>" : "<EMAIL>"}
                className={errors.email ? "border-destructive" : ""}
              />
              {errors.email && <p className="text-sm text-destructive">{errors.email}</p>}
            </div>

            {/* Password */}
            <div className="space-y-2">
              <Label htmlFor="password">{language === "ar" ? "كلمة المرور" : "Password"} *</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  placeholder={language === "ar" ? "كلمة مرور قوية" : "Strong password"}
                  className={`pr-10 ${errors.password ? "border-destructive" : ""}`}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
              {errors.password && <p className="text-sm text-destructive">{errors.password}</p>}
            </div>

            {/* Confirm Password */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">{language === "ar" ? "تأكيد كلمة المرور" : "Confirm Password"} *</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                placeholder={language === "ar" ? "أعد إدخال كلمة المرور" : "Re-enter password"}
                className={errors.confirmPassword ? "border-destructive" : ""}
              />
              {errors.confirmPassword && <p className="text-sm text-destructive">{errors.confirmPassword}</p>}
            </div>

            {/* Role and Status Grid */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="role">{language === "ar" ? "الدور" : "Role"} *</Label>
                <Select value={formData.role} onValueChange={(value: any) => setFormData({ ...formData, role: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر الدور" : "Select role"} />
                  </SelectTrigger>
                  <SelectContent>
                    {roleOptions.map((role) => (
                      <SelectItem key={role.value} value={role.value}>
                        {role.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="user_status">{language === "ar" ? "الحالة" : "Status"} *</Label>
                <Select value={formData.user_status} onValueChange={(value: any) => setFormData({ ...formData, user_status: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر الحالة" : "Select status"} />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Job Title */}
            <div className="space-y-2">
              <Label htmlFor="job_title">{language === "ar" ? "المسمى الوظيفي" : "Job Title"}</Label>
              <Input
                id="job_title"
                value={formData.job_title}
                onChange={(e) => setFormData({ ...formData, job_title: e.target.value })}
                placeholder={language === "ar" ? "مدير العمليات" : "Operations Manager"}
              />
            </div>

            {/* Branch */}
            <div className="space-y-2">
              <Label htmlFor="branch_id">{language === "ar" ? "الفرع" : "Branch"}</Label>
              <Select value={formData.branch_id || "no-branch"} onValueChange={(value) => setFormData({ ...formData, branch_id: value === "no-branch" ? "" : value })}>
                <SelectTrigger>
                  <SelectValue placeholder={language === "ar" ? "اختر الفرع (اختياري)" : "Select branch (optional)"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="no-branch">{language === "ar" ? "بدون فرع" : "No branch"}</SelectItem>
                  {branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              {language === "ar" ? "إلغاء" : "Cancel"}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (language === "ar" ? "جاري الإضافة..." : "Adding...") : language === "ar" ? "إضافة" : "Add"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}