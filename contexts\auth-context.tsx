"use client"

import { createContext, useContext, useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { createClient } from '@/lib/supabase-browser'
import { useRouter } from 'next/navigation'

interface AuthContextType {
  user: User | null
  isLoading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  signUp: (email: string, password: string) => Promise<void>
  refreshSession: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({} as AuthContextType)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  
  // Only create Supabase client on client-side
  const supabase = typeof window !== 'undefined' ? createClient() : null

  useEffect(() => {
    // Only run on client-side
    if (!supabase) {
      setIsLoading(false)
      return
    }

    // Check active sessions and sets the user
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null)
      setIsLoading(false)
    })

    // Listen for changes on auth state (signed in, signed out, etc.)
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null)
      setIsLoading(false)
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [supabase])

  const signIn = async (email: string, password: string) => {
    if (!supabase) throw new Error("Supabase client not available")
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    if (error) throw error
    
    // Update the user state immediately after successful login
    if (data.session?.user) {
      setUser(data.session.user)
      setIsLoading(false)
    }
  }

  const signUp = async (email: string, password: string) => {
    if (!supabase) throw new Error("Supabase client not available")
    const { error } = await supabase.auth.signUp({
      email,
      password,
    })
    if (error) throw error
    // Direct navigation is now handled in the component
  }

  const signOut = async () => {
    if (!supabase) throw new Error("Supabase client not available")
    const { error } = await supabase.auth.signOut()
    if (error) throw error
    // Direct navigation to login page
    router.push('/login')
  }

  const refreshSession = async () => {
    if (!supabase) throw new Error("Supabase client not available")
    const { error } = await supabase.auth.refreshSession()
    if (error) {
      console.warn("Failed to refresh session:", error)
      // Don't throw error to prevent app crashes
    }
  }

  return (
    <AuthContext.Provider value={{ user, isLoading, signIn, signOut, signUp, refreshSession }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}