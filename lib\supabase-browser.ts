import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  // During build time, environment variables might not be available
  // Use dummy values for build process
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://dummy.supabase.co'
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'dummy-key'
  
  return createBrowserClient(supabaseUrl, supabaseKey)
}