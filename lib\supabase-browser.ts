import { createBrowserClient } from '@supabase/ssr'

// Singleton pattern to ensure only one Supabase client instance
let supabaseClient: ReturnType<typeof createBrowserClient> | null = null

export function createClient() {
  // Return existing client if already created
  if (supabaseClient) {
    return supabaseClient
  }

  // During build time, environment variables might not be available
  // Use dummy values for build process
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://dummy.supabase.co'
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'dummy-key'

  // Create client with optimized configuration to prevent unwanted refreshes
  supabaseClient = createBrowserClient(supabaseUrl, supabaseKey, {
    auth: {
      // Disable automatic token refresh on focus to prevent unwanted page refreshes
      autoRefreshToken: false,
      // Keep session persistence for better UX
      persistSession: true,
      // Disable session detection in URL to prevent unnecessary refreshes
      detectSessionInUrl: false,
      // Use PKCE flow for better security
      flowType: 'pkce',
    },
    global: {
      headers: {
        'x-application-name': 'fleet-management-dashboard',
      },
    },
  })

  return supabaseClient
}