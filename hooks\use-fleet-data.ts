"use client"

import { useState, useEffect } from "react"
import {
  type DashboardOverviewAll,
  type FleetOverview,
  type Vehicle,
  type Driver,
  type User,
  type Alert,
  type UpcomingService,
  type MaintenanceStatistics,
} from "@/lib/supabase"
import { createClient } from "@/lib/supabase-browser"

export function useFleetKPIs() {
  const [data, setData] = useState<DashboardOverviewAll | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchKPIs() {
      try {
        setLoading(true)
        const supabase = createClient()
        const { data: kpis, error } = await supabase.from("mv_dashboard_overview_all").select("*").single()

        if (error) throw error
        setData(kpis)
      } catch (err) {
        console.error("Error fetching KPIs:", err)
        setError(err instanceof Error ? err.message : "An error occurred")
      } finally {
        setLoading(false)
      }
    }

    fetchKPIs()
  }, [])

  return { data, loading, error }
}

export function useFleetOverview() {
  const [data, setData] = useState<FleetOverview | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchOverview() {
      try {
        setLoading(true)
        const supabase = createClient()
        const { data: overview, error } = await supabase.from("mv_fleet_overview").select("*").single()

        if (error) throw error
        setData(overview)
      } catch (err) {
        console.error("Error fetching fleet overview:", err)
        setError(err instanceof Error ? err.message : "An error occurred")
      } finally {
        setLoading(false)
      }
    }

    fetchOverview()
  }, [])

  return { data, loading, error }
}

export function useMaintenanceStatistics() {
  const [data, setData] = useState<MaintenanceStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchStats() {
      try {
        setLoading(true)
        const supabase = createClient()
        const { data: stats, error } = await supabase.from("mv_maintenance_statistics").select("*").single()

        if (error) throw error
        setData(stats)
      } catch (err) {
        console.error("Error fetching maintenance statistics:", err)
        setError(err instanceof Error ? err.message : "An error occurred")
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  return { data, loading, error }
}

export function useVehicles(limit = 50) {
  const [data, setData] = useState<Vehicle[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refetchTrigger, setRefetchTrigger] = useState(0)

  const refetch = () => setRefetchTrigger(prev => prev + 1)

  useEffect(() => {
    async function fetchVehicles() {
      try {
        setLoading(true)
        const supabase = createClient()
        const { data: vehicles, error } = await supabase
          .from("vehicles")
          .select("id, plate_number, color, vehicle_type, service_type, fuel_type, year, vin, current_km, last_maintenance_km, next_maintenance_km, last_tire_change_km, license_expiry, branch_id, status, created_at, license_type, vehicle_features, permits")
          .order("created_at", { ascending: false })
          .limit(limit)

        if (error) throw error
        setData(vehicles || [])
      } catch (err) {
        console.error("Error fetching vehicles:", err)
        setError(err instanceof Error ? err.message : "An error occurred")
      } finally {
        setLoading(false)
      }
    }

    fetchVehicles()
  }, [limit, refetchTrigger])

  return { data, loading, error, refetch }
}

export function useDrivers(limit = 50) {
  const [data, setData] = useState<Driver[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchDrivers() {
      try {
        setLoading(true)
        const supabase = createClient()
        const { data: drivers, error } = await supabase
          .from("drivers")
          .select("*")
          .order("performance_score", { ascending: false })
          .limit(limit)

        if (error) throw error
        setData(drivers || [])
      } catch (err) {
        console.error("Error fetching drivers:", err)
        setError(err instanceof Error ? err.message : "An error occurred")
      } finally {
        setLoading(false)
      }
    }

    fetchDrivers()
  }, [limit])

  return { data, loading, error }
}

export function useRecentAlerts(limit = 20) {
  const [data, setData] = useState<Alert[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchAlerts() {
      try {
        setLoading(true)
        const supabase = createClient()
        const { data: alerts, error } = await supabase
          .from("alerts")
          .select("*")
          .order("created_at", { ascending: false })
          .limit(limit)

        if (error) throw error
        setData(alerts || [])
      } catch (err) {
        console.error("Error fetching alerts:", err)
        setError(err instanceof Error ? err.message : "An error occurred")
      } finally {
        setLoading(false)
      }
    }

    fetchAlerts()
  }, [limit])

  return { data, loading, error }
}

// Hook to fetch upcoming services with manual refresh capability
export function useUpcomingServices(limit = 50) {
  const [data, setData] = useState<UpcomingService[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const refreshServices = async () => {
    try {
      setLoading(true)
      const supabase = createClient()
      
      // Try to refresh the materialized view
      const { error: refreshError } = await supabase.rpc('refresh_materialized_views')
      
      if (refreshError) {
        console.warn("Could not refresh materialized views:", refreshError)
      }
      
      // Fetch the data
      const { data: services, error } = await supabase.from("mv_upcoming_services").select("*").limit(limit)

      if (error) {
        // Check if the error is due to the view not existing
        if (error.message && (error.message.includes('relation "mv_upcoming_services" does not exist') || error.message.includes('mv_upcoming_services'))) {
          console.warn("Materialized view mv_upcoming_services does not exist. Falling back to regular query.")
          // Fallback to a regular query on the vehicles table with similar logic
          const { data: fallbackData, error: fallbackError } = await supabase
            .from("vehicles")
            .select("id, vehicle_type, plate_number, current_km, next_maintenance_date, next_maintenance_km")
            .limit(limit)
            
          if (fallbackError) {
            console.error("Fallback query also failed:", fallbackError)
            throw new Error(`Failed to fetch data: ${fallbackError.message || JSON.stringify(fallbackError)}`)
          }
          
          // Transform the fallback data to match the UpcomingService interface
          const transformedData: UpcomingService[] = (fallbackData || []).map(item => ({
            vehicle_id: item.id,
            vehicle_type: item.vehicle_type,
            plate_number: item.plate_number,
            current_km: item.current_km,
            service_type: 'maintenance',
            due_date: item.next_maintenance_date,
            due_km: item.next_maintenance_km,
            priority: item.next_maintenance_km ? 'scheduled' : 'unknown',
            vehicle_status: 'active',
            last_updated: new Date().toISOString()
          }))
          
          setData(transformedData)
          setError("Materialized view not available, showing limited data")
        } else {
          console.error("Supabase error details:", error)
          throw new Error(`Database error: ${error.message || JSON.stringify(error)}`)
        }
      } else {
        setData(services || [])
        setError(null)
      }
    } catch (err) {
      console.error("Error fetching upcoming services:", err)
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred"
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    refreshServices()
  }, [limit])

  return { data, loading, error, refreshServices }
}

export function useFuelTrends(days = 30) {
  const [data, setData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchFuelTrends() {
      try {
        setLoading(true)
        const supabase = createClient()
        const endDate = new Date()
        const startDate = new Date()
        startDate.setDate(startDate.getDate() - days)

        const { data: fuelLogs, error } = await supabase
          .from("fuel_logs")
          .select("fuel_date, liters, cost_egp")
          .gte("fuel_date", startDate.toISOString().split("T")[0])
          .lte("fuel_date", endDate.toISOString().split("T")[0])
          .order("fuel_date", { ascending: true })

        if (error) throw error

        // Group by date and sum values
        const groupedData = (fuelLogs || []).reduce((acc: any, log: any) => {
          const date = log.fuel_date
          if (!acc[date]) {
            acc[date] = { fuel_date: date, total_liters: 0, total_cost_egp: 0 }
          }
          acc[date].total_liters += log.liters
          acc[date].total_cost_egp += log.cost_egp
          return acc
        }, {})

        setData(Object.values(groupedData))
      } catch (err) {
        console.error("Error fetching fuel trends:", err)
        setError(err instanceof Error ? err.message : "An error occurred")
      } finally {
        setLoading(false)
      }
    }

    fetchFuelTrends()
  }, [days])

  return { data, loading, error }
}

export function useMaintenanceCosts(months = 12) {
  const [data, setData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchMaintenanceCosts() {
      try {
        setLoading(true)
        const supabase = createClient()
        const endDate = new Date()
        const startDate = new Date()
        startDate.setMonth(startDate.getMonth() - months)

        const { data: maintenance, error } = await supabase
          .from("maintenance")
          .select("service_date, total_cost_egp, service_type")
          .gte("service_date", startDate.toISOString().split("T")[0])
          .lte("service_date", endDate.toISOString().split("T")[0])
          .order("service_date", { ascending: true })

        if (error) throw error

        // Group by month and sum costs
        const groupedData = (maintenance || []).reduce((acc: any, record: any) => {
          const monthYear = record.service_date.substring(0, 7) // YYYY-MM
          if (!acc[monthYear]) {
            acc[monthYear] = { month_year: monthYear, total_maintenance_cost_egp: 0, service_count: 0 }
          }
          acc[monthYear].total_maintenance_cost_egp += record.total_cost_egp || 0
          acc[monthYear].service_count += 1
          return acc
        }, {})

        setData(Object.values(groupedData))
      } catch (err) {
        console.error("Error fetching maintenance costs:", err)
        setError(err instanceof Error ? err.message : "An error occurred")
      } finally {
        setLoading(false)
      }
    }

    fetchMaintenanceCosts()
  }, [months])

  return { data, loading, error }
}

export function useVehicleStatusDistribution() {
  const [data, setData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchStatusDistribution() {
      try {
        setLoading(true)
        const supabase = createClient()
        const { data: vehicles, error } = await supabase.from("vehicles").select("status")

        if (error) throw error

        // Group by status and count
        const statusCounts = (vehicles || []).reduce((acc: any, vehicle: any) => {
          const status = vehicle.status || "unknown"
          acc[status] = (acc[status] || 0) + 1
          return acc
        }, {})

        const total = Object.values(statusCounts).reduce((sum: number, count: any) => sum + count, 0)

        const distribution = Object.entries(statusCounts).map(([status, count]: [string, any]) => ({
          status,
          count,
          percentage: total > 0 ? Math.round((count / total) * 100) : 0,
        }))

        setData(distribution)
      } catch (err) {
        console.error("Error fetching vehicle status distribution:", err)
        setError(err instanceof Error ? err.message : "An error occurred")
      } finally {
        setLoading(false)
      }
    }

    fetchStatusDistribution()
  }, [])

  return { data, loading, error }
}

export function useUsers(limit = 50) {
  const [data, setData] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refetchTrigger, setRefetchTrigger] = useState(0)

  const refetch = () => setRefetchTrigger(prev => prev + 1)

  useEffect(() => {
    async function fetchUsers() {
      try {
        setLoading(true)
        const supabase = createClient()
        const { data: users, error } = await supabase
          .from("users")
          .select(`
            *,
            branch:branches(name)
          `)
          .order("created_at", { ascending: false })
          .limit(limit)

        if (error) throw error
        setData(users || [])
      } catch (err) {
        console.error("Error fetching users:", err)
        setError(err instanceof Error ? err.message : "An error occurred")
      } finally {
        setLoading(false)
      }
    }

    fetchUsers()
  }, [limit, refetchTrigger])

  return { data, loading, error, refetch }
}

// Hook to fetch current authenticated user's profile data
export function useCurrentUserProfile() {
  const [data, setData] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [userEmail, setUserEmail] = useState<string | null>(null)
  const [userId, setUserId] = useState<string | null>(null)

  // Create browser client for auth operations
  const browserClient = createClient()

  // Get current user's ID and email from auth session
  useEffect(() => {
    async function getCurrentUser() {
      try {
        const { data: { session } } = await browserClient.auth.getSession()
        if (session?.user) {
          setUserId(session.user.id)
          setUserEmail(session.user.email || null)
          console.log('Auth: Session found for', session.user.email)
        } else {
          setUserId(null)
          setUserEmail(null)
          console.log('Auth: No session found')
        }
      } catch (err) {
        console.error("Error getting current user session:", err)
      }
    }

    getCurrentUser()

    // Listen for auth state changes (minimal logging)
    const { data: { subscription } } = browserClient.auth.onAuthStateChange((_event: any, session: any) => {
      const newUserId = session?.user?.id || null
      const newUserEmail = session?.user?.email || null

      // Only update if something actually changed
      if (newUserId !== userId || newUserEmail !== userEmail) {
        setUserId(newUserId)
        setUserEmail(newUserEmail)

        if (!newUserId) {
          setData(null)
          console.log('Auth: User logged out')
        } else if (newUserId !== userId) {
          console.log('Auth: User changed to', newUserEmail)
        }
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  // Fetch user profile data when user ID is available
  useEffect(() => {
    async function fetchCurrentUserProfile() {
      if (!userId) {
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        setError(null)
        
        // Fetching user profile silently
        
        // Use User_ID for optimal performance and security
        const supabase = createClient()
        const { data: user, error } = await supabase
          .from("users")
          .select(`
            *,
            branch:branches(name)
          `)
          .eq("id", userId)
          .maybeSingle()

        if (error) {
          console.error('Database query error:', error)
          throw new Error(`Database error: ${error.message || 'Unknown database error'}`)
        }

        if (!user) {
          console.warn('No user record found for ID:', userId)
          // For security, don't expose the actual user ID in error messages
          setData(null)
          setError(`No user profile found. Please contact administrator.`)
        } else {
          console.log('Profile loaded for:', user.full_name || user.email)
          setData(user)
          setError(null)
        }
      } catch (err) {
        console.error("Error fetching current user profile:", err)
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
        setError(errorMessage)
        setData(null)
      } finally {
        setLoading(false)
      }
    }

    fetchCurrentUserProfile()
  }, [userId])

  return { data, loading, error, userEmail, userId }
}
