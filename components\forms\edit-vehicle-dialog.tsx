"use client"

import React, { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Upload, X } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase-browser"
import { useAuth } from "@/contexts/auth-context"
import { uploadMultipleLicenseImages, getVehicleLicenseImages } from "@/lib/vehicle-license-service"

// Minimal vehicle interface based on confirmed database fields
interface Vehicle {
  id: string
  plate_number: string
  color?: string
  vehicle_type?: string
  service_type?: string
  fuel_type?: string
  year?: number
  vin?: string
  current_km?: number
  last_maintenance_km?: number
  last_tire_change_km?: number
  license_expiry?: string
  branch_id?: string
  status: string
  created_at: string
  license_front_image?: string
  license_back_image?: string
  license_type?: string
  vehicle_features?: string
  permits?: string
}

interface EditVehicleDialogProps {
  vehicle: Vehicle
  language: "ar" | "en"
  open: boolean
  onClose: () => void
  onVehicleUpdated?: () => void
}

export function EditVehicleDialog({ vehicle, language, open, onClose, onVehicleUpdated }: EditVehicleDialogProps) {
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()
  const { user } = useAuth()
  const supabaseClient = createClient()
  
  // File input refs
  const frontImageRef = useRef<HTMLInputElement>(null)
  const backImageRef = useRef<HTMLInputElement>(null)
  
  // License image states
  const [licenseImages, setLicenseImages] = useState<{
    front?: File;
    back?: File;
  }>({})
  
  const [licenseImagePreviews, setLicenseImagePreviews] = useState<{
    front?: string;
    back?: string;
  }>({})
  
  const [currentLicenseImages, setCurrentLicenseImages] = useState<{
    front?: string;
    back?: string;
  }>({})

  // Form state with only confirmed database fields
  const [formData, setFormData] = useState({
    plate_number: "",
    color: "",
    vehicle_type: "",
    service_type: "",
    fuel_type: "",
    year: "",
    vin: "",
    current_km: "",
    last_maintenance_km: "",
    last_tire_change_km: "",
    license_expiry: "",
    branch_id: "",
    status: "active",
    license_type: "",
    vehicle_features: "",
    permits: "",
  })

  const [branches, setBranches] = useState<Array<{ id: string; name: string }>>([])

  // Load vehicle data when dialog opens
  useEffect(() => {
    if (open && vehicle) {
      // Log incoming vehicle data to debug
      console.log('Loading vehicle data:', vehicle)
      console.log('Vehicle fields:', Object.keys(vehicle))
      console.log('Contains vehicle_type?', 'vehicle_type' in vehicle)

      setFormData({
        plate_number: vehicle.plate_number || "",
        color: vehicle.color || "",
        vehicle_type: vehicle.vehicle_type || "",
        service_type: vehicle.service_type || "",
        fuel_type: vehicle.fuel_type || "",
        year: vehicle.year ? vehicle.year.toString() : "",
        vin: vehicle.vin || "",
        current_km: vehicle.current_km ? vehicle.current_km.toString() : "",
        last_maintenance_km: vehicle.last_maintenance_km ? vehicle.last_maintenance_km.toString() : "",
        last_tire_change_km: vehicle.last_tire_change_km ? vehicle.last_tire_change_km.toString() : "",
        license_expiry: vehicle.license_expiry || "",
        branch_id: vehicle.branch_id || "",
        status: vehicle.status || "active",
        license_type: vehicle.license_type || "",
        vehicle_features: vehicle.vehicle_features || "",
        permits: vehicle.permits || "",
      })
      
      // Load existing license images
      loadExistingLicenseImages(vehicle.id)
    }
  }, [open, vehicle])
  
  // Load existing license images from database
  const loadExistingLicenseImages = async (vehicleId: string) => {
    try {
      const imageData = await getVehicleLicenseImages(vehicleId)
      
      if (!imageData.error) {
        setCurrentLicenseImages({
          front: imageData.frontImage,
          back: imageData.backImage
        })
      }
    } catch (error) {
      console.warn('Failed to load existing license images:', error)
    }
  }
  
  // Handle license image selection
  const handleLicenseImageChange = (side: 'front' | 'back', file: File | null) => {
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: language === "ar" ? "نوع ملف غير مدعوم" : "Unsupported file type",
          description: language === "ar" ? "يرجى استخدام JPEG, PNG أو WebP" : "Please use JPEG, PNG or WebP",
          variant: "destructive"
        })
        return
      }
      
      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: language === "ar" ? "حجم الملف كبير" : "File too large",
          description: language === "ar" ? "الحد الأقصى 5 ميجابايت" : "Maximum size is 5MB",
          variant: "destructive"
        })
        return
      }
      
      // Update license images state
      setLicenseImages(prev => ({ ...prev, [side]: file }))
      
      // Create preview URL
      const previewUrl = URL.createObjectURL(file)
      setLicenseImagePreviews(prev => {
        // Clean up old preview URL
        if (prev[side]) {
          URL.revokeObjectURL(prev[side]!)
        }
        return { ...prev, [side]: previewUrl }
      })
    } else {
      // Remove image
      setLicenseImages(prev => {
        const newState = { ...prev }
        delete newState[side]
        return newState
      })
      
      // Clean up preview URL
      setLicenseImagePreviews(prev => {
        if (prev[side]) {
          URL.revokeObjectURL(prev[side]!)
        }
        const newState = { ...prev }
        delete newState[side]
        return newState
      })
    }
  }
  
  // Remove license image
  const removeLicenseImage = (side: 'front' | 'back') => {
    handleLicenseImageChange(side, null)
    
    // Reset file input
    if (side === 'front' && frontImageRef.current) {
      frontImageRef.current.value = ''
    } else if (side === 'back' && backImageRef.current) {
      backImageRef.current.value = ''
    }
  }

  // Fetch branches
  useEffect(() => {
    if (open) {
      fetchBranches()
    }
  }, [open])

  const fetchBranches = async () => {
    try {
      const { data, error } = await supabaseClient
        .from('branches')
        .select('id, name')
        .order('name')
      
      if (error) {
        console.error('Error fetching branches:', error.message)
      } else {
        setBranches(data || [])
      }
    } catch (error) {
      console.error('Error fetching branches:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Basic validation
      if (!user) {
        throw new Error(language === "ar" ? "يجب تسجيل الدخول أولاً" : "Please log in first")
      }

      if (!formData.plate_number.trim()) {
        throw new Error(language === "ar" ? "رقم اللوحة مطلوب" : "Plate number is required")
      }

      // Prepare update data with only confirmed database fields
      const updateData: Record<string, any> = {
        plate_number: formData.plate_number.trim(),
        status: formData.status || "active"
      }

      // Add optional fields only if they have values
      if (formData.color?.trim()) {
        updateData.color = formData.color.trim()
      }
      
      if (formData.vehicle_type?.trim()) {
        updateData.vehicle_type = formData.vehicle_type.trim()
      }
      
      if (formData.service_type?.trim()) {
        updateData.service_type = formData.service_type.trim()
      }
      
      if (formData.fuel_type?.trim()) {
        updateData.fuel_type = formData.fuel_type.trim()
      }
      
      if (formData.year?.trim()) {
        const yearNum = parseInt(formData.year.trim())
        if (!isNaN(yearNum)) {
          updateData.year = yearNum
        }
      }
      
      if (formData.vin?.trim()) {
        updateData.vin = formData.vin.trim()
      }
      
      if (formData.current_km?.trim()) {
        const kmNum = parseInt(formData.current_km.trim())
        if (!isNaN(kmNum)) {
          updateData.current_km = kmNum
        }
      }
      
      if (formData.last_maintenance_km?.trim()) {
        const kmNum = parseInt(formData.last_maintenance_km.trim())
        if (!isNaN(kmNum)) {
          updateData.last_maintenance_km = kmNum
        }
      }
      
      if (formData.last_tire_change_km?.trim()) {
        const kmNum = parseInt(formData.last_tire_change_km.trim())
        if (!isNaN(kmNum)) {
          updateData.last_tire_change_km = kmNum
        }
      }
      
      if (formData.license_expiry?.trim()) {
        updateData.license_expiry = formData.license_expiry.trim()
      }
      
      if (formData.branch_id?.trim()) {
        updateData.branch_id = formData.branch_id.trim()
      }
      
      if (formData.license_type?.trim()) {
        updateData.license_type = formData.license_type.trim()
      }
      
      if (formData.vehicle_features?.trim()) {
        updateData.vehicle_features = formData.vehicle_features.trim()
      }
      
      if (formData.permits?.trim()) {
        updateData.permits = formData.permits.trim()
      }

      // Additional safety: Only allow known safe fields
      const safeFields = [
        'plate_number', 'color', 'vehicle_type', 'service_type', 'fuel_type', 'year',
        'vin', 'current_km', 'last_maintenance_km', 'last_tire_change_km',
        'license_expiry', 'branch_id', 'status', 'license_type', 'vehicle_features', 'permits'
      ]

      const filteredUpdateData: Record<string, any> = {}
      safeFields.forEach(field => {
        if (updateData[field] !== undefined) {
          filteredUpdateData[field] = updateData[field]
        }
      })

      console.log('Original update data:', updateData)
      console.log('Filtered update data:', filteredUpdateData)
      console.log('Fields being updated:', Object.keys(filteredUpdateData))
      console.log('Does filtered data contain vehicle_type?', 'vehicle_type' in filteredUpdateData)

      // Perform the update with filtered data
      const { data, error } = await supabaseClient
        .from("vehicles")
        .update(filteredUpdateData)
        .eq("id", vehicle.id)
        .select()

      if (error) {
        console.error('Update error:', error.message)
        throw new Error(error.message)
      }

      if (!data || data.length === 0) {
        throw new Error(language === "ar" ? "فشل تحديث البيانات" : "Failed to update data")
      }

      console.log('Update successful:', data[0])

      // Upload license images if new ones provided
      if (licenseImages.front || licenseImages.back) {
        console.log('📸 Starting license image uploads for vehicle update...')
        
        const uploadResult = await uploadMultipleLicenseImages(vehicle.id, licenseImages)
        
        if (!uploadResult.success) {
          console.warn('⚠️ Some license images failed to upload:', uploadResult.error)
          
          toast({
            title: language === "ar" ? "تم تحديث المركبة مع تحذير" : "Vehicle Updated with Warning",
            description: 
              language === "ar"
                ? `تم تحديث المركبة ${formData.plate_number} لكن فشل رفع بعض صور الرخصة`
                : `Vehicle ${formData.plate_number} updated but some license images failed to upload`,
            variant: "default"
          })
        } else {
          console.log('✅ All license images uploaded successfully')
          
          toast({
            title: language === "ar" ? "تم تحديث المركبة" : "Vehicle Updated",
            description:
              language === "ar"
                ? `تم تحديث المركبة ${formData.plate_number} مع صور الرخصة بنجاح`
                : `Vehicle ${formData.plate_number} has been updated with license images successfully`,
          })
        }
      } else {
        // No new images to upload
        toast({
          title: language === "ar" ? "تم تحديث المركبة" : "Vehicle Updated",
          description:
            language === "ar"
              ? `تم تحديث المركبة ${formData.plate_number} بنجاح`
              : `Vehicle ${formData.plate_number} has been updated successfully`,
        })
      }

      // Clean up preview URLs
      Object.values(licenseImagePreviews).forEach(url => {
        if (url) URL.revokeObjectURL(url)
      })
      setLicenseImages({})
      setLicenseImagePreviews({})
      
      // Close dialog and trigger refresh
      onClose()
      if (onVehicleUpdated) {
        onVehicleUpdated()
      }

    } catch (error: any) {
      console.error('Vehicle update error:', error.message)
      
      toast({
        title: language === "ar" ? "خطأ" : "Error",
        description: error.message || (language === "ar" ? "حدث خطأ أثناء تحديث المركبة" : "An error occurred while updating the vehicle"),
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>{language === "ar" ? "تعديل المركبة" : "Edit Vehicle"}</DialogTitle>
          <DialogDescription>
            {language === "ar" ? "تعديل تفاصيل المركبة" : "Edit vehicle details"}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto">
          <div className="grid gap-4 py-4">
            {/* Plate Number - Required */}
            <div className="space-y-2">
              <Label htmlFor="edit_plate_number">{language === "ar" ? "رقم اللوحة" : "Plate Number"} *</Label>
              <Input
                id="edit_plate_number"
                value={formData.plate_number}
                onChange={(e) => setFormData({ ...formData, plate_number: e.target.value })}
                placeholder={language === "ar" ? "ABC-123" : "ABC-123"}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Color */}
              <div className="space-y-2">
                <Label htmlFor="edit_color">{language === "ar" ? "اللون" : "Color"}</Label>
                <Select
                  value={formData.color}
                  onValueChange={(value) => setFormData({ ...formData, color: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر اللون" : "Select color"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Black">{language === "ar" ? "أسود" : "Black"}</SelectItem>
                    <SelectItem value="White">{language === "ar" ? "أبيض" : "White"}</SelectItem>
                    <SelectItem value="Silver">{language === "ar" ? "فضي" : "Silver"}</SelectItem>
                    <SelectItem value="Grey">{language === "ar" ? "رمادي" : "Grey"}</SelectItem>
                    <SelectItem value="Blue">{language === "ar" ? "أزرق" : "Blue"}</SelectItem>
                    <SelectItem value="Red">{language === "ar" ? "أحمر" : "Red"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Vehicle Type */}
              <div className="space-y-2">
                <Label htmlFor="edit_vehicle_type">{language === "ar" ? "نوع المركبة" : "Vehicle Type"}</Label>
                <Select
                  value={formData.vehicle_type}
                  onValueChange={(value) => setFormData({ ...formData, vehicle_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر النوع" : "Select type"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="E-Cab">{language === "ar" ? "كاب" : "E-Cab"}</SelectItem>
                    <SelectItem value="Comfort">{language === "ar" ? "كومفورت" : "Comfort"}</SelectItem>
                    <SelectItem value="Comfort+">{language === "ar" ? "كومفورت بلص" : "Comfort+"}</SelectItem>
                    <SelectItem value="Compact">{language === "ar" ? "كومبكت" : "Compact"}</SelectItem>
                    <SelectItem value="Premium">{language === "ar" ? "برايميوم" : "Premium"}</SelectItem>
                    <SelectItem value="E-Class">{language === "ar" ? "مرسيدس" : "E-Class"}</SelectItem>
                    <SelectItem value="Van">{language === "ar" ? "فان" : "Van"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Service Type */}
              <div className="space-y-2">
                <Label htmlFor="edit_service_type">{language === "ar" ? "نوع الخدمة" : "Service Type"}</Label>
                <Select
                  value={formData.service_type}
                  onValueChange={(value) => setFormData({ ...formData, service_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر الخدمة" : "Select service"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="LEVC">{language === "ar" ? "ليفك" : "LEVC"}</SelectItem>
                    <SelectItem value="Comfort">{language === "ar" ? "كومفورت" : "Comfort"}</SelectItem>
                    <SelectItem value="Comfort+">{language === "ar" ? "كومفورت بلص" : "Comfort+"}</SelectItem>
                    <SelectItem value="Compact">{language === "ar" ? "كومبكت" : "Compact"}</SelectItem>
                    <SelectItem value="Premium">{language === "ar" ? "برايميوم" : "Premium"}</SelectItem>
                    <SelectItem value="E-Class">{language === "ar" ? "مرسيدس" : "E-Class"}</SelectItem>
                    <SelectItem value="Van">{language === "ar" ? "فان" : "Van"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {/* Fuel Type */}
              <div className="space-y-2">
                <Label htmlFor="edit_fuel_type">{language === "ar" ? "نوع الوقود" : "Fuel Type"}</Label>
                <Select
                  value={formData.fuel_type}
                  onValueChange={(value) => setFormData({ ...formData, fuel_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر الوقود" : "Select fuel"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gasoline_95">{language === "ar" ? "بنزين 95" : "Gasoline 95"}</SelectItem>
                    <SelectItem value="gasoline_92">{language === "ar" ? "بنزين 92" : "Gasoline 92"}</SelectItem>
                    <SelectItem value="diesel">{language === "ar" ? "ديزل" : "Diesel"}</SelectItem>
                    <SelectItem value="electric">{language === "ar" ? "كهربائي" : "Electric"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Year */}
              <div className="space-y-2">
                <Label htmlFor="edit_year">{language === "ar" ? "سنة الصنع" : "Year"}</Label>
                <Input
                  id="edit_year"
                  type="number"
                  value={formData.year}
                  onChange={(e) => setFormData({ ...formData, year: e.target.value })}
                  placeholder="2023"
                  min="1990"
                  max="2030"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_license_type">{language === "ar" ? "نوع الترخيص" : "License Type"}</Label>
                <Select
                  value={formData.license_type}
                  onValueChange={(value) => setFormData({ ...formData, license_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر نوع الترخيص" : "Select license type"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Private">{language === "ar" ? "خاص" : "Private"}</SelectItem>
                    <SelectItem value="Tourism">{language === "ar" ? "سياحي" : "Tourism"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit_vehicle_features">{language === "ar" ? "تفاصيل المركبة" : "Vehicle Features"}</Label>
                <Select
                  value={formData.vehicle_features}
                  onValueChange={(value) => setFormData({ ...formData, vehicle_features: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر التفاصيل" : "Select features"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Ramp">{language === "ar" ? "رامب" : "Ramp"}</SelectItem>
                    <SelectItem value="No Ramp">{language === "ar" ? "بدون رامب" : "No Ramp"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit_permits">{language === "ar" ? "التصاريح" : "Permits"}</Label>
                <Select
                  value={formData.permits}
                  onValueChange={(value) => setFormData({ ...formData, permits: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر التصاريح" : "Select permits"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Airport Permit">{language === "ar" ? "تصريح مطار" : "Airport Permit"}</SelectItem>
                    <SelectItem value="None">{language === "ar" ? "لا يوجد" : "None"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* VIN */}
              <div className="space-y-2">
                <Label htmlFor="edit_vin">{language === "ar" ? "رقم الهيكل" : "VIN"}</Label>
                <Input
                  id="edit_vin"
                  value={formData.vin}
                  onChange={(e) => setFormData({ ...formData, vin: e.target.value })}
                  placeholder="1HGBH41JXMN109186"
                />
              </div>

              {/* Current KM */}
              <div className="space-y-2">
                <Label htmlFor="edit_current_km">{language === "ar" ? "الكيلومترات الحالية" : "Current KM"}</Label>
                <Input
                  id="edit_current_km"
                  type="number"
                  value={formData.current_km}
                  onChange={(e) => setFormData({ ...formData, current_km: e.target.value })}
                  placeholder="50000"
                  min="0"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Last Maintenance KM */}
              <div className="space-y-2">
                <Label htmlFor="edit_last_maintenance_km">{language === "ar" ? "آخر صيانة (كم)" : "Last Maintenance (KM)"}</Label>
                <Input
                  id="edit_last_maintenance_km"
                  type="number"
                  value={formData.last_maintenance_km}
                  onChange={(e) => setFormData({ ...formData, last_maintenance_km: e.target.value })}
                  placeholder="45000"
                  min="0"
                />
              </div>

              {/* Last Tire Change KM */}
              <div className="space-y-2">
                <Label htmlFor="edit_last_tire_change_km">{language === "ar" ? "آخر تغيير إطارات (كم)" : "Last Tire Change (KM)"}</Label>
                <Input
                  id="edit_last_tire_change_km"
                  type="number"
                  value={formData.last_tire_change_km}
                  onChange={(e) => setFormData({ ...formData, last_tire_change_km: e.target.value })}
                  placeholder="30000"
                  min="0"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* License Expiry */}
              <div className="space-y-2">
                <Label htmlFor="edit_license_expiry">{language === "ar" ? "تاريخ انتهاء الرخصة" : "License Expiry Date"}</Label>
                <Input
                  id="edit_license_expiry"
                  type="date"
                  value={formData.license_expiry}
                  onChange={(e) => setFormData({ ...formData, license_expiry: e.target.value })}
                />
              </div>

              {/* Branch */}
              <div className="space-y-2">
                <Label htmlFor="edit_branch_id">{language === "ar" ? "الفرع" : "Branch"}</Label>
                <Select
                  value={formData.branch_id}
                  onValueChange={(value) => setFormData({ ...formData, branch_id: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={language === "ar" ? "اختر الفرع" : "Select branch"} />
                  </SelectTrigger>
                  <SelectContent>
                    {branches.map((branch) => (
                      <SelectItem key={branch.id} value={branch.id}>
                        {branch.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Label htmlFor="edit_status">{language === "ar" ? "الحالة" : "Status"}</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData({ ...formData, status: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">{language === "ar" ? "نشط" : "Active"}</SelectItem>
                  <SelectItem value="maintenance">{language === "ar" ? "صيانة" : "Maintenance"}</SelectItem>
                  <SelectItem value="out_of_service">{language === "ar" ? "خارج الخدمة" : "Out of Service"}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* License Images Section */}
            <div className="space-y-4">
              <div className="border-t pt-4">
                <h4 className="font-medium mb-4">{language === "ar" ? "صور الرخصة" : "License Images"}</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Front License Image */}
                  <div className="space-y-3">
                    <Label>{language === "ar" ? "الوجه الأمامي للرخصة" : "Front License"}</Label>
                    
                    {/* Current Image Display */}
                    {currentLicenseImages.front && !licenseImagePreviews.front && (
                      <div className="relative">
                        <img 
                          src={currentLicenseImages.front} 
                          alt={language === "ar" ? "الوجه الأمامي للرخصة الحالي" : "Current front license"}
                          className="w-full h-32 object-cover rounded-lg border"
                        />
                        <div className="absolute top-2 right-2">
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            onClick={() => {
                              setCurrentLicenseImages(prev => ({ ...prev, front: undefined }))
                              if (frontImageRef.current) {
                                frontImageRef.current.click()
                              }
                            }}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {language === "ar" ? "الصورة الحالية" : "Current image"}
                        </p>
                      </div>
                    )}
                    
                    {/* New Image Preview */}
                    {licenseImagePreviews.front && (
                      <div className="relative">
                        <img 
                          src={licenseImagePreviews.front} 
                          alt={language === "ar" ? "معاينة الوجه الأمامي" : "Front license preview"}
                          className="w-full h-32 object-cover rounded-lg border"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2"
                          onClick={() => removeLicenseImage('front')}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                        <p className="text-xs text-green-600 mt-1">
                          {language === "ar" ? "صورة جديدة" : "New image"}
                        </p>
                      </div>
                    )}
                    
                    {/* Upload Interface */}
                    {!licenseImagePreviews.front && (
                      <div 
                        className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 transition-colors"
                        onClick={() => frontImageRef.current?.click()}
                      >
                        <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                        <p className="text-sm text-gray-600">
                          {language === "ar" ? "انقر لرفع صورة جديدة" : "Click to upload new image"}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {language === "ar" ? "JPEG, PNG, WebP (حد أقصى 5MB)" : "JPEG, PNG, WebP (max 5MB)"}
                        </p>
                      </div>
                    )}
                    
                    <input
                      ref={frontImageRef}
                      type="file"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0] || null
                        handleLicenseImageChange('front', file)
                      }}
                    />
                  </div>
                  
                  {/* Back License Image */}
                  <div className="space-y-3">
                    <Label>{language === "ar" ? "الوجه الخلفي للرخصة" : "Back License"}</Label>
                    
                    {/* Current Image Display */}
                    {currentLicenseImages.back && !licenseImagePreviews.back && (
                      <div className="relative">
                        <img 
                          src={currentLicenseImages.back} 
                          alt={language === "ar" ? "الوجه الخلفي للرخصة الحالي" : "Current back license"}
                          className="w-full h-32 object-cover rounded-lg border"
                        />
                        <div className="absolute top-2 right-2">
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            onClick={() => {
                              setCurrentLicenseImages(prev => ({ ...prev, back: undefined }))
                              if (backImageRef.current) {
                                backImageRef.current.click()
                              }
                            }}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {language === "ar" ? "الصورة الحالية" : "Current image"}
                        </p>
                      </div>
                    )}
                    
                    {/* New Image Preview */}
                    {licenseImagePreviews.back && (
                      <div className="relative">
                        <img 
                          src={licenseImagePreviews.back} 
                          alt={language === "ar" ? "معاينة الوجه الخلفي" : "Back license preview"}
                          className="w-full h-32 object-cover rounded-lg border"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2"
                          onClick={() => removeLicenseImage('back')}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                        <p className="text-xs text-green-600 mt-1">
                          {language === "ar" ? "صورة جديدة" : "New image"}
                        </p>
                      </div>
                    )}
                    
                    {/* Upload Interface */}
                    {!licenseImagePreviews.back && (
                      <div 
                        className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 transition-colors"
                        onClick={() => backImageRef.current?.click()}
                      >
                        <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                        <p className="text-sm text-gray-600">
                          {language === "ar" ? "انقر لرفع صورة جديدة" : "Click to upload new image"}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {language === "ar" ? "JPEG, PNG, WebP (حد أقصى 5MB)" : "JPEG, PNG, WebP (max 5MB)"}
                        </p>
                      </div>
                    )}
                    
                    <input
                      ref={backImageRef}
                      type="file"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0] || null
                        handleLicenseImageChange('back', file)
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Vehicle Info */}
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">{language === "ar" ? "معلومات إضافية" : "Additional Information"}</h4>
              <div className="text-sm text-muted-foreground space-y-1">
                <p><strong>{language === "ar" ? "تاريخ الإنشاء:" : "Created:"}</strong> {new Date(vehicle.created_at).toLocaleDateString()}</p>
              </div>
            </div>
          </div>

          <DialogFooter className="mt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              {language === "ar" ? "إلغاء" : "Cancel"}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (language === "ar" ? "جاري التحديث..." : "Updating...") : language === "ar" ? "تحديث" : "Update"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
