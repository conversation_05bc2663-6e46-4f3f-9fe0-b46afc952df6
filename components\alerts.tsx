"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>riangle, CheckCircle, Clock, Bell } from "lucide-react"
import { useRecentAlerts } from "@/hooks/use-fleet-data"
import { LoadingSpinner } from "@/components/loading-spinner"
import { ErrorDisplay } from "@/components/error-display"
import { UpcomingServices } from "@/components/upcoming-services"

interface AlertsProps {
  language: "ar" | "en"
}

export function Alerts({ language }: AlertsProps) {
  const { data: alerts, loading, error } = useRecentAlerts(20)

  if (loading) {
    return <LoadingSpinner className="h-64" />
  }

  if (error) {
    return <ErrorDisplay error={error} />
  }

  const getSeverityBadge = (severity: string) => {
    const severityConfig = {
      low: { variant: "outline" as const, label: language === "ar" ? "منخفض" : "Low", icon: Bell },
      medium: { variant: "secondary" as const, label: language === "ar" ? "متوسط" : "Medium", icon: Clock },
      high: { variant: "default" as const, label: language === "ar" ? "عالي" : "High", icon: AlertTriangle },
      critical: { variant: "destructive" as const, label: language === "ar" ? "حرج" : "Critical", icon: AlertTriangle },
    }

    const config = severityConfig[severity as keyof typeof severityConfig] || severityConfig.medium
    const Icon = config.icon

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    )
  }

  const getStatusIcon = (alert: any) => {
    if (alert.resolved) {
      return <CheckCircle className="h-5 w-5 text-green-500" />
    } else if (alert.acknowledged) {
      return <Clock className="h-5 w-5 text-yellow-500" />
    } else {
      return <AlertTriangle className="h-5 w-5 text-red-500" />
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">{language === "ar" ? "التنبيهات" : "Alerts"}</h2>
        <p className="text-muted-foreground">
          {language === "ar" ? "تنبيهات وإشعارات الأسطول" : "Fleet alerts and notifications"}
        </p>
      </div>

      {/* Alert Summary */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {language === "ar" ? "إجمالي التنبيهات" : "Total Alerts"}
            </CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{alerts.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{language === "ar" ? "غير محلولة" : "Unresolved"}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">{alerts.filter((a) => !a.resolved).length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{language === "ar" ? "حرجة" : "Critical"}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">
              {alerts.filter((a) => a.severity === "critical").length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{language === "ar" ? "محلولة" : "Resolved"}</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{alerts.filter((a) => a.resolved).length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts List */}
      <Card>
        <CardHeader>
          <CardTitle>{language === "ar" ? "التنبيهات الحديثة" : "Recent Alerts"}</CardTitle>
          <CardDescription>
            {language === "ar" ? "آخر التنبيهات والإشعارات" : "Latest alerts and notifications"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {alerts.map((alert) => (
              <div key={alert.id} className="flex items-start gap-4 p-4 border rounded-lg">
                <div className="mt-1">{getStatusIcon(alert)}</div>
                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{alert.alert_type}</h4>
                    {getSeverityBadge(alert.severity)}
                  </div>
                  <p className="text-sm text-muted-foreground">{alert.message}</p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>{new Date(alert.created_at).toLocaleDateString(language === "ar" ? "ar-EG" : "en-EG")}</span>
                    {alert.vehicle_id && (
                      <span>
                        {language === "ar" ? "المركبة:" : "Vehicle:"} {alert.vehicle_id}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex gap-2">
                  {!alert.acknowledged && (
                    <Button variant="outline" size="sm">
                      {language === "ar" ? "قراءة" : "Acknowledge"}
                    </Button>
                  )}
                  {!alert.resolved && (
                    <Button variant="default" size="sm">
                      {language === "ar" ? "حل" : "Resolve"}
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Upcoming Services */}
      <UpcomingServices language={language} />
    </div>
  )
}
